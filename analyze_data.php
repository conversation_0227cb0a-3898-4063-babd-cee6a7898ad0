<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Kost;
use App\Models\KostFacility;
use App\Models\KostImage;
use App\Models\User;

echo "=== ENHANCED JEMBER KOST DATA ANALYSIS ===\n\n";

// Basic Statistics
echo "📊 BASIC STATISTICS:\n";
echo "Total Kost: " . Kost::count() . "\n";
echo "Total Facilities: " . KostFacility::count() . "\n";
echo "Total Images: " . KostImage::count() . "\n";
echo "Total Users: " . User::count() . "\n\n";

// Gender Distribution
echo "👥 GENDER DISTRIBUTION:\n";
$genderStats = Kost::selectRaw('gender_type, count(*) as count')
    ->groupBy('gender_type')
    ->get();

foreach ($genderStats as $stat) {
    $percentage = round(($stat->count / Kost::count()) * 100, 1);
    echo "- {$stat->gender_type}: {$stat->count} ({$percentage}%)\n";
}
echo "\n";

// Price Range Analysis
echo "💰 PRICE RANGE ANALYSIS:\n";
$budget = Kost::whereBetween('price_monthly', [0, 500000])->count();
$standard = Kost::whereBetween('price_monthly', [500001, 800000])->count();
$premium = Kost::whereBetween('price_monthly', [800001, 1200000])->count();
$executive = Kost::where('price_monthly', '>', 1200000)->count();

$total = Kost::count();
echo "- Budget (≤500k): {$budget} (" . round(($budget/$total)*100, 1) . "%)\n";
echo "- Standard (500k-800k): {$standard} (" . round(($standard/$total)*100, 1) . "%)\n";
echo "- Premium (800k-1.2M): {$premium} (" . round(($premium/$total)*100, 1) . "%)\n";
echo "- Executive (>1.2M): {$executive} (" . round(($executive/$total)*100, 1) . "%)\n\n";

// Average Statistics
echo "📈 AVERAGE STATISTICS:\n";
echo "Average Price: Rp " . number_format(Kost::avg('price_monthly'), 0, ',', '.') . "\n";
echo "Average Room Count: " . round(Kost::avg('room_count'), 1) . "\n";
echo "Average Available Rooms: " . round(Kost::avg('available_rooms'), 1) . "\n";
echo "Total Rooms: " . Kost::sum('room_count') . "\n";
echo "Total Available: " . Kost::sum('available_rooms') . "\n\n";

// Top Facilities
echo "🏠 TOP FACILITIES:\n";
$topFacilities = KostFacility::selectRaw('name, count(*) as count')
    ->groupBy('name')
    ->orderBy('count', 'desc')
    ->limit(10)
    ->get();

foreach ($topFacilities as $facility) {
    $percentage = round(($facility->count / Kost::count()) * 100, 1);
    echo "- {$facility->name}: {$facility->count} ({$percentage}%)\n";
}
echo "\n";

// Facility Categories
echo "📋 FACILITY CATEGORIES:\n";
$categories = KostFacility::selectRaw('category, count(*) as count')
    ->groupBy('category')
    ->orderBy('count', 'desc')
    ->get();

foreach ($categories as $category) {
    echo "- {$category->category}: {$category->count}\n";
}
echo "\n";

// Area Distribution (simulated based on address patterns)
echo "📍 AREA DISTRIBUTION:\n";
$areas = [
    'Sumbersari' => Kost::where('address', 'like', '%Sumbersari%')->count(),
    'Kaliwates' => Kost::where('address', 'like', '%Kaliwates%')->count(),
    'Patrang' => Kost::where('address', 'like', '%Patrang%')->count(),
    'Wuluhan' => Kost::where('address', 'like', '%Wuluhan%')->count(),
    'Arjasa' => Kost::where('address', 'like', '%Arjasa%')->count(),
    'Others' => Kost::where('address', 'not like', '%Sumbersari%')
        ->where('address', 'not like', '%Kaliwates%')
        ->where('address', 'not like', '%Patrang%')
        ->where('address', 'not like', '%Wuluhan%')
        ->where('address', 'not like', '%Arjasa%')
        ->count(),
];

foreach ($areas as $area => $count) {
    if ($count > 0) {
        $percentage = round(($count / Kost::count()) * 100, 1);
        echo "- {$area}: {$count} ({$percentage}%)\n";
    }
}
echo "\n";

// Status Distribution
echo "📊 STATUS DISTRIBUTION:\n";
$statusStats = Kost::selectRaw('status, count(*) as count')
    ->groupBy('status')
    ->get();

foreach ($statusStats as $stat) {
    $percentage = round(($stat->count / Kost::count()) * 100, 1);
    echo "- {$stat->status}: {$stat->count} ({$percentage}%)\n";
}
echo "\n";

// Availability Analysis
echo "🏠 AVAILABILITY ANALYSIS:\n";
$fullKost = Kost::where('available_rooms', 0)->count();
$availableKost = Kost::where('available_rooms', '>', 0)->count();

echo "- Full Kost (0 available): {$fullKost} (" . round(($fullKost/$total)*100, 1) . "%)\n";
echo "- Available Kost (>0 available): {$availableKost} (" . round(($availableKost/$total)*100, 1) . "%)\n\n";

// Price by Gender
echo "💰 AVERAGE PRICE BY GENDER:\n";
$priceByGender = Kost::selectRaw('gender_type, AVG(price_monthly) as avg_price')
    ->groupBy('gender_type')
    ->get();

foreach ($priceByGender as $stat) {
    echo "- {$stat->gender_type}: Rp " . number_format($stat->avg_price, 0, ',', '.') . "\n";
}
echo "\n";

echo "=== ANALYSIS COMPLETE ===\n";
echo "Data successfully enhanced with " . Kost::count() . " kost entries from Mamikos research!\n";
