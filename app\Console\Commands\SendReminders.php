<?php

namespace App\Console\Commands;

use App\Services\NotificationService;
use Illuminate\Console\Command;

class SendReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'kost:send-reminders {--type=all : Type of reminders to send (all, kost-submission, inquiry-response)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send reminder notifications to users';

    private NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        parent::__construct();
        $this->notificationService = $notificationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->option('type');

        $this->info('📢 Starting reminder notifications...');

        try {
            $totalSent = 0;

            if ($type === 'all' || $type === 'kost-submission') {
                $this->info('📝 Sending kost submission reminders...');
                $kostReminders = $this->notificationService->sendKostSubmissionReminder();
                $this->info("   ✅ Sent {$kostReminders} kost submission reminders");
                $totalSent += $kostReminders;
            }

            if ($type === 'all' || $type === 'inquiry-response') {
                $this->info('💬 Sending inquiry response reminders...');
                $inquiryReminders = $this->notificationService->sendUnrespondedInquiryReminder();
                $this->info("   ✅ Sent {$inquiryReminders} inquiry response reminders");
                $totalSent += $inquiryReminders;
            }

            if ($type !== 'all' && $type !== 'kost-submission' && $type !== 'inquiry-response') {
                $this->error('❌ Invalid reminder type. Use: all, kost-submission, or inquiry-response');
                return Command::FAILURE;
            }

            $this->info("🎉 Total reminders sent: {$totalSent}");

        } catch (\Exception $e) {
            $this->error('❌ An error occurred: ' . $e->getMessage());
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
}
