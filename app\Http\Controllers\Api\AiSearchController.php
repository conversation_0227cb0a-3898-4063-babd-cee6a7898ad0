<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\GroqAiService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class AiSearchController extends Controller
{
    private GroqAiService $groqAiService;

    public function __construct(GroqAiService $groqAiService)
    {
        $this->groqAiService = $groqAiService;
    }

    /**
     * Pencarian kost menggunakan AI
     */
    public function search(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'query' => 'required|string|min:3|max:500',
        ], [
            'query.required' => 'Query pencarian harus diisi.',
            'query.min' => 'Query pencarian minimal 3 karakter.',
            'query.max' => 'Query pencarian maksimal 500 karakter.',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal.',
                'errors' => $validator->errors(),
            ], 422);
        }

        // Cek apakah AI service tersedia
        if (!$this->groqAiService->isAvailable()) {
            return response()->json([
                'success' => false,
                'message' => 'AI service tidak tersedia saat ini.',
            ], 503);
        }

        try {
            $userId = Auth::id();
            $query = $request->input('query');

            $result = $this->groqAiService->searchKosts($query, $userId);

            return response()->json([
                'success' => $result['success'],
                'message' => $result['success']
                    ? 'Pencarian berhasil.'
                    : ($result['error'] ?? 'Pencarian gagal.'),
                'data' => [
                    'query' => $query,
                    'results' => $result['results'],
                    'ai_interpretation' => $result['search_criteria']['interpretation'] ?? null,
                    'search_criteria' => $result['search_criteria']['criteria'] ?? null,
                    'recommendations' => $result['search_criteria']['recommendations'] ?? [],
                    'response_time' => round($result['response_time'], 3),
                    'total_results' => count($result['results']),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan pada server.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Mendapatkan riwayat pencarian user
     */
    public function history(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            $history = $user->aiSearchLogs()
                ->latest()
                ->limit(20)
                ->get()
                ->map(function ($log) {
                    return [
                        'id' => $log->id,
                        'query' => $log->query,
                        'results_count' => $log->results_count,
                        'response_time' => $log->formatted_response_time,
                        'created_at' => $log->created_at->format('d/m/Y H:i'),
                    ];
                });

            return response()->json([
                'success' => true,
                'message' => 'Riwayat pencarian berhasil diambil.',
                'data' => $history,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan pada server.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Mendapatkan statistik pencarian user
     */
    public function stats(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $stats = \App\Models\AiSearchLog::getUserSearchStats($user->id);

            return response()->json([
                'success' => true,
                'message' => 'Statistik pencarian berhasil diambil.',
                'data' => [
                    'total_searches' => $stats['total_searches'],
                    'average_response_time' => $stats['avg_response_time'] ? round($stats['avg_response_time'], 3) . 's' : 'N/A',
                    'total_results_found' => $stats['total_results'],
                    'last_search' => $stats['last_search'] ? $stats['last_search']->format('d/m/Y H:i') : 'Belum ada pencarian',
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan pada server.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }
}
