<?php

use App\Http\Controllers\Api\AiSearchController;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\FileUploadController;
use App\Http\Controllers\Api\InquiryController;
use App\Http\Controllers\Api\KostController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// Authentication Routes (Public)
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/logout', [AuthController::class, 'logout'])->middleware('auth:sanctum');

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Public Routes (no authentication required)
Route::prefix('kosts')->group(function () {
    Route::get('/', [KostController::class, 'index']);
    Route::get('/popular', [KostController::class, 'popular']);
    Route::get('/latest', [KostController::class, 'latest']);
    Route::get('/cities', [KostController::class, 'cities']);
    Route::get('/price-stats', [KostController::class, 'priceStats']);
    Route::get('/city/{city}', [KostController::class, 'byCity']);
    Route::get('/{kost}', [KostController::class, 'show'])->where('kost', '[0-9]+');
    Route::get('/{kost}/similar', [KostController::class, 'similar'])->where('kost', '[0-9]+');
});

// Protected Routes (authentication required)
Route::middleware(['auth:sanctum'])->group(function () {
    // Kost Routes for Owners
    Route::get('/my-kosts', [KostController::class, 'myKosts']);

    // Inquiry Routes
    Route::apiResource('inquiries', InquiryController::class);
    Route::patch('inquiries/{inquiry}/status', [InquiryController::class, 'updateStatus']);
    Route::get('inquiry-stats', [InquiryController::class, 'stats']);

    // AI Search Routes
    Route::prefix('ai-search')->group(function () {
        Route::post('/search', [AiSearchController::class, 'search']);
        Route::get('/history', [AiSearchController::class, 'history']);
        Route::get('/stats', [AiSearchController::class, 'stats']);
    });

    // File Upload Routes
    Route::prefix('upload')->group(function () {
        Route::post('/image', [FileUploadController::class, 'uploadImage']);
        Route::post('/images', [FileUploadController::class, 'uploadMultipleImages']);
        Route::delete('/image', [FileUploadController::class, 'deleteImage']);
        Route::get('/image-info', [FileUploadController::class, 'getImageInfo']);
        Route::post('/cleanup-unused', [FileUploadController::class, 'cleanupUnusedImages']);
    });
});
