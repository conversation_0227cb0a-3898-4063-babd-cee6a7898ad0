<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string  ...$roles
     */
    public function handle(Request $request, Closure $next, string ...$roles): Response
    {
        // Pastikan user sudah login
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();

        // Pastikan user aktif
        if (!$user->is_active) {
            Auth::logout();
            return redirect()->route('login')->with('error', 'Akun Anda tidak aktif. Silakan hubungi administrator.');
        }

        // Cek apakah user memiliki role yang diizinkan
        if (!empty($roles) && !in_array($user->role, $roles)) {
            // Redirect berdasarkan role user
            return $this->redirectBasedOnRole($user->role);
        }

        return $next($request);
    }

    /**
     * Redirect user berdasarkan role mereka
     */
    private function redirectBasedOnRole(string $role): Response
    {
        return match ($role) {
            'pencari_kost' => redirect()->route('pencari.dashboard')->with('error', 'Anda tidak memiliki akses ke halaman tersebut.'),
            'pemilik_kost' => redirect()->route('pemilik.dashboard')->with('error', 'Anda tidak memiliki akses ke halaman tersebut.'),
            'admin' => redirect()->route('admin.dashboard')->with('error', 'Anda tidak memiliki akses ke halaman tersebut.'),
            default => redirect()->route('dashboard')->with('error', 'Anda tidak memiliki akses ke halaman tersebut.'),
        };
    }
}
