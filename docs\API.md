# 📡 <PERSON>IM Kost API Documentation

## Base URL
```
Development: http://localhost:8000/api
Production: https://your-domain.com/api
```

## Authentication

S<PERSON> Kost uses Laravel Sanctum for API authentication. Include the Bearer token in the Authorization header:

```
Authorization: Bearer your-token-here
```

### Authentication Endpoints

#### Register User
```http
POST /register
```

**Request Body:**
```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "password": "password123",
  "password_confirmation": "password123",
  "role": "pencari_kost", // or "pemilik_kost"
  "phone": "081234567890"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Registration successful",
  "data": {
    "user": {
      "id": 1,
      "name": "<PERSON>",
      "email": "<EMAIL>",
      "role": "pencari_kost"
    },
    "token": "1|abc123..."
  }
}
```

#### Login
```http
POST /login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "pencari_kost"
    },
    "token": "1|abc123..."
  }
}
```

## Kost Endpoints

### Get All Kosts
```http
GET /kosts
```

**Query Parameters:**
- `city` (string): Filter by city
- `gender_type` (string): putra, putri, campur
- `min_price` (integer): Minimum monthly price
- `max_price` (integer): Maximum monthly price
- `facilities` (array): Filter by facilities
- `search` (string): Search in name, description, address
- `sort_by` (string): created_at, price_monthly, name, available_rooms
- `sort_order` (string): asc, desc
- `per_page` (integer): Items per page (max 50)

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "name": "Kost Melati",
      "description": "Kost nyaman di pusat kota",
      "city": "Jakarta",
      "province": "DKI Jakarta",
      "price_monthly": 2000000,
      "formatted_price": "Rp 2.000.000",
      "available_rooms": 5,
      "gender_type": "campur",
      "owner": {
        "id": 2,
        "name": "Jane Smith",
        "email": "<EMAIL>"
      },
      "facilities": [...],
      "images": [...]
    }
  ],
  "links": {...},
  "meta": {...}
}
```

### Get Single Kost
```http
GET /kosts/{id}
```

**Response:**
```json
{
  "id": 1,
  "name": "Kost Melati",
  "description": "Kost nyaman di pusat kota",
  "address": "Jl. Sudirman No. 123",
  "city": "Jakarta",
  "province": "DKI Jakarta",
  "price_monthly": 2000000,
  "price_daily": 80000,
  "room_count": 20,
  "available_rooms": 5,
  "gender_type": "campur",
  "kost_type": "keduanya",
  "status": "approved",
  "is_active": true,
  "owner": {...},
  "facilities": [...],
  "images": [...]
}
```

### Get Popular Kosts
```http
GET /kosts/popular?limit=10
```

### Get Latest Kosts
```http
GET /kosts/latest?limit=10
```

### Get Available Cities
```http
GET /kosts/cities
```

**Response:**
```json
{
  "success": true,
  "data": ["Jakarta", "Bandung", "Surabaya", "Yogyakarta"]
}
```

### Get Price Statistics
```http
GET /kosts/price-stats
```

**Response:**
```json
{
  "success": true,
  "data": {
    "min_price": 500000,
    "max_price": 5000000,
    "avg_price": 1750000,
    "total_kosts": 150,
    "price_ranges": [
      {
        "label": "Di bawah Rp 1 juta",
        "min": 0,
        "max": 1000000
      }
    ]
  }
}
```

### Get My Kosts (Owner Only)
```http
GET /my-kosts
```

**Headers:**
```
Authorization: Bearer your-token-here
```

## AI Search Endpoints

### AI-Powered Search
```http
POST /ai-search/search
```

**Headers:**
```
Authorization: Bearer your-token-here
```

**Request Body:**
```json
{
  "query": "Cari kost putra di Jakarta dengan harga di bawah 2 juta yang ada WiFi"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Search completed successfully",
  "data": {
    "query": "Cari kost putra di Jakarta...",
    "results": [...],
    "ai_interpretation": "User mencari kost khusus putra...",
    "search_criteria": {
      "interpretation": "User mencari kost khusus putra di Jakarta dengan budget maksimal 2 juta dan fasilitas WiFi",
      "criteria": {
        "city": "Jakarta",
        "gender_type": "putra",
        "max_price": 2000000,
        "facilities": ["WiFi"]
      },
      "recommendations": [...]
    },
    "response_time": 1.25,
    "total_results": 8
  }
}
```

### Get Search History
```http
GET /ai-search/history?per_page=20
```

**Headers:**
```
Authorization: Bearer your-token-here
```

### Get Search Statistics
```http
GET /ai-search/stats
```

**Headers:**
```
Authorization: Bearer your-token-here
```

## Inquiry Endpoints

### Get All Inquiries
```http
GET /inquiries
```

**Headers:**
```
Authorization: Bearer your-token-here
```

**Query Parameters:**
- `status` (string): pending, responded, closed
- `kost_id` (integer): Filter by kost
- `per_page` (integer): Items per page

### Create Inquiry
```http
POST /inquiries
```

**Headers:**
```
Authorization: Bearer your-token-here
```

**Request Body:**
```json
{
  "kost_id": 1,
  "message": "Apakah masih ada kamar kosong? Saya tertarik untuk melihat kamar.",
  "contact_preference": "whatsapp"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Inquiry berhasil dikirim",
  "data": {
    "id": 1,
    "kost_id": 1,
    "user_id": 1,
    "message": "Apakah masih ada kamar kosong?...",
    "contact_preference": "whatsapp",
    "status": "pending",
    "created_at": "2024-01-15T10:30:00Z",
    "kost": {...},
    "user": {...}
  }
}
```

### Update Inquiry Status (Owner Only)
```http
PATCH /inquiries/{id}/status
```

**Headers:**
```
Authorization: Bearer your-token-here
```

**Request Body:**
```json
{
  "status": "responded"
}
```

### Delete Inquiry
```http
DELETE /inquiries/{id}
```

**Headers:**
```
Authorization: Bearer your-token-here
```

## File Upload Endpoints

### Upload Single Image
```http
POST /upload/image
```

**Headers:**
```
Authorization: Bearer your-token-here
Content-Type: multipart/form-data
```

**Request Body:**
```
image: (file) - Image file (max 2MB, JPEG/PNG/WEBP)
alt_text: (string, optional) - Alt text for image
```

### Upload Multiple Images
```http
POST /upload/images
```

**Headers:**
```
Authorization: Bearer your-token-here
Content-Type: multipart/form-data
```

**Request Body:**
```
images[]: (files) - Array of image files (max 10 files)
```

## Error Responses

### Validation Error (422)
```json
{
  "success": false,
  "message": "Validasi gagal",
  "errors": {
    "email": ["Email sudah digunakan"],
    "password": ["Password minimal 8 karakter"]
  }
}
```

### Unauthorized (401)
```json
{
  "success": false,
  "message": "Unauthenticated"
}
```

### Forbidden (403)
```json
{
  "success": false,
  "message": "Akses ditolak"
}
```

### Not Found (404)
```json
{
  "success": false,
  "message": "Resource tidak ditemukan"
}
```

### Server Error (500)
```json
{
  "success": false,
  "message": "Terjadi kesalahan server",
  "error": "Error details (only in debug mode)"
}
```

## Rate Limiting

API endpoints are rate limited:
- **Authentication endpoints**: 5 requests per minute
- **AI Search**: 10 requests per minute
- **Other endpoints**: 60 requests per minute

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: 1640995200
```

## Pagination

List endpoints return paginated results:

```json
{
  "data": [...],
  "links": {
    "first": "http://localhost:8000/api/kosts?page=1",
    "last": "http://localhost:8000/api/kosts?page=10",
    "prev": null,
    "next": "http://localhost:8000/api/kosts?page=2"
  },
  "meta": {
    "current_page": 1,
    "from": 1,
    "last_page": 10,
    "per_page": 15,
    "to": 15,
    "total": 150
  }
}
```

## Webhooks (Coming Soon)

SIM Kost will support webhooks for real-time notifications:
- New inquiry received
- Kost status changed
- User registration

---

For more information, contact our API support <NAME_EMAIL>
