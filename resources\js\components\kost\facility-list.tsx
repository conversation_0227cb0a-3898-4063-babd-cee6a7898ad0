import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { KostFacility } from '@/types';
import { 
    Bed, 
    Bath, 
    Wifi, 
    Shield, 
    Car, 
    Star,
    Home,
    Zap,
    Droplets,
    Wind,
    Tv,
    Coffee,
    Utensils,
    Shirt,
    Lock,
    Camera,
    Lightbulb
} from 'lucide-react';
import React from 'react';

interface FacilityListProps {
    facilities: KostFacility[];
    showCategories?: boolean;
    layout?: 'grid' | 'list' | 'compact';
    className?: string;
}

const FacilityList: React.FC<FacilityListProps> = ({
    facilities,
    showCategories = true,
    layout = 'grid',
    className = '',
}) => {
    const getFacilityIcon = (facilityName: string, category: string) => {
        const name = facilityName.toLowerCase();
        
        // Specific facility icons
        if (name.includes('wifi') || name.includes('internet')) return <Wifi className="h-4 w-4" />;
        if (name.includes('ac') || name.includes('air conditioning')) return <Wind className="h-4 w-4" />;
        if (name.includes('tv') || name.includes('televisi')) return <Tv className="h-4 w-4" />;
        if (name.includes('kulkas') || name.includes('refrigerator')) return <Coffee className="h-4 w-4" />;
        if (name.includes('dapur') || name.includes('kitchen')) return <Utensils className="h-4 w-4" />;
        if (name.includes('laundry') || name.includes('cuci')) return <Shirt className="h-4 w-4" />;
        if (name.includes('listrik') || name.includes('electricity')) return <Zap className="h-4 w-4" />;
        if (name.includes('air') || name.includes('water')) return <Droplets className="h-4 w-4" />;
        if (name.includes('lampu') || name.includes('light')) return <Lightbulb className="h-4 w-4" />;
        if (name.includes('cctv') || name.includes('camera')) return <Camera className="h-4 w-4" />;
        if (name.includes('kunci') || name.includes('lock')) return <Lock className="h-4 w-4" />;
        
        // Category-based icons
        switch (category) {
            case 'kamar': return <Bed className="h-4 w-4" />;
            case 'kamar_mandi': return <Bath className="h-4 w-4" />;
            case 'umum': return <Home className="h-4 w-4" />;
            case 'keamanan': return <Shield className="h-4 w-4" />;
            case 'parkir': return <Car className="h-4 w-4" />;
            default: return <Star className="h-4 w-4" />;
        }
    };

    const getCategoryColor = (category: string) => {
        switch (category) {
            case 'kamar': return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
            case 'kamar_mandi': return 'bg-cyan-100 text-cyan-800 hover:bg-cyan-200';
            case 'umum': return 'bg-green-100 text-green-800 hover:bg-green-200';
            case 'keamanan': return 'bg-red-100 text-red-800 hover:bg-red-200';
            case 'parkir': return 'bg-purple-100 text-purple-800 hover:bg-purple-200';
            default: return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
        }
    };

    const getCategoryName = (category: string) => {
        switch (category) {
            case 'kamar': return 'Fasilitas Kamar';
            case 'kamar_mandi': return 'Kamar Mandi';
            case 'umum': return 'Fasilitas Umum';
            case 'keamanan': return 'Keamanan';
            case 'parkir': return 'Parkir';
            default: return 'Lainnya';
        }
    };

    if (facilities.length === 0) {
        return (
            <Card className={className}>
                <CardContent className="flex items-center justify-center py-8">
                    <div className="text-center text-muted-foreground">
                        <Star className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p>Belum ada fasilitas yang ditambahkan</p>
                    </div>
                </CardContent>
            </Card>
        );
    }

    // Group facilities by category
    const groupedFacilities = facilities.reduce((acc, facility) => {
        if (!acc[facility.category]) {
            acc[facility.category] = [];
        }
        acc[facility.category].push(facility);
        return acc;
    }, {} as Record<string, KostFacility[]>);

    if (layout === 'compact') {
        return (
            <div className={`flex flex-wrap gap-2 ${className}`}>
                {facilities.map((facility) => (
                    <div
                        key={facility.id}
                        className="flex items-center gap-2 px-3 py-2 bg-secondary/50 rounded-lg text-sm"
                    >
                        {getFacilityIcon(facility.name, facility.category)}
                        <span>{facility.name}</span>
                    </div>
                ))}
            </div>
        );
    }

    if (layout === 'list') {
        return (
            <Card className={className}>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Star className="h-5 w-5" />
                        Fasilitas Kost
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="space-y-3">
                        {facilities.map((facility, index) => (
                            <div key={facility.id}>
                                <div className="flex items-start gap-3">
                                    <div className="flex-shrink-0 mt-0.5">
                                        {getFacilityIcon(facility.name, facility.category)}
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <div className="flex items-center gap-2">
                                            <h4 className="font-medium text-sm">{facility.name}</h4>
                                            {showCategories && (
                                                <Badge 
                                                    variant="secondary" 
                                                    className={`text-xs ${getCategoryColor(facility.category)}`}
                                                >
                                                    {getCategoryName(facility.category)}
                                                </Badge>
                                            )}
                                        </div>
                                        {facility.description && (
                                            <p className="text-sm text-muted-foreground mt-1">
                                                {facility.description}
                                            </p>
                                        )}
                                    </div>
                                </div>
                                {index < facilities.length - 1 && <Separator className="mt-3" />}
                            </div>
                        ))}
                    </div>
                </CardContent>
            </Card>
        );
    }

    // Grid layout (default)
    return (
        <div className={`space-y-6 ${className}`}>
            {showCategories ? (
                // Grouped by categories
                Object.entries(groupedFacilities).map(([category, categoryFacilities]) => (
                    <Card key={category}>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2 text-lg">
                                {getFacilityIcon('', category)}
                                {getCategoryName(category)}
                                <Badge variant="secondary" className="ml-auto">
                                    {categoryFacilities.length}
                                </Badge>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                                {categoryFacilities.map((facility) => (
                                    <div
                                        key={facility.id}
                                        className="flex items-center gap-3 p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
                                    >
                                        <div className="flex-shrink-0">
                                            {getFacilityIcon(facility.name, facility.category)}
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <h4 className="font-medium text-sm truncate">
                                                {facility.name}
                                            </h4>
                                            {facility.description && (
                                                <p className="text-xs text-muted-foreground truncate">
                                                    {facility.description}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                ))
            ) : (
                // All facilities in one grid
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Star className="h-5 w-5" />
                            Fasilitas Kost
                            <Badge variant="secondary" className="ml-auto">
                                {facilities.length}
                            </Badge>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                            {facilities.map((facility) => (
                                <div
                                    key={facility.id}
                                    className="flex items-center gap-3 p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
                                >
                                    <div className="flex-shrink-0">
                                        {getFacilityIcon(facility.name, facility.category)}
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <h4 className="font-medium text-sm truncate">
                                            {facility.name}
                                        </h4>
                                        {facility.description && (
                                            <p className="text-xs text-muted-foreground truncate">
                                                {facility.description}
                                            </p>
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            )}
        </div>
    );
};

export default FacilityList;
