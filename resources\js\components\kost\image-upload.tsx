import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { KostImage } from '@/types';
import { Upload, X, Image as ImageIcon, Star } from 'lucide-react';
import React, { useCallback, useState } from 'react';

interface ImageUploadProps {
    existingImages?: KostImage[];
    onImagesChange: (newImages: File[], removedImageIds: number[]) => void;
    maxImages?: number;
    className?: string;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
    existingImages = [],
    onImagesChange,
    maxImages = 10,
    className = '',
}) => {
    const [newImages, setNewImages] = useState<File[]>([]);
    const [removedImageIds, setRemovedImageIds] = useState<number[]>([]);
    const [dragActive, setDragActive] = useState(false);

    const handleDrag = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        if (e.type === 'dragenter' || e.type === 'dragover') {
            setDragActive(true);
        } else if (e.type === 'dragleave') {
            setDragActive(false);
        }
    }, []);

    const handleDrop = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setDragActive(false);

        if (e.dataTransfer.files && e.dataTransfer.files[0]) {
            handleFiles(Array.from(e.dataTransfer.files));
        }
    }, []);

    const handleFiles = (files: File[]) => {
        const imageFiles = files.filter(file => file.type.startsWith('image/'));
        const currentImageCount = existingImages.length - removedImageIds.length + newImages.length;
        const availableSlots = maxImages - currentImageCount;
        const filesToAdd = imageFiles.slice(0, availableSlots);

        if (filesToAdd.length > 0) {
            const updatedNewImages = [...newImages, ...filesToAdd];
            setNewImages(updatedNewImages);
            onImagesChange(updatedNewImages, removedImageIds);
        }
    };

    const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files) {
            handleFiles(Array.from(e.target.files));
        }
    };

    const removeNewImage = (index: number) => {
        const updatedNewImages = newImages.filter((_, i) => i !== index);
        setNewImages(updatedNewImages);
        onImagesChange(updatedNewImages, removedImageIds);
    };

    const removeExistingImage = (imageId: number) => {
        const updatedRemovedIds = [...removedImageIds, imageId];
        setRemovedImageIds(updatedRemovedIds);
        onImagesChange(newImages, updatedRemovedIds);
    };

    const restoreExistingImage = (imageId: number) => {
        const updatedRemovedIds = removedImageIds.filter(id => id !== imageId);
        setRemovedImageIds(updatedRemovedIds);
        onImagesChange(newImages, updatedRemovedIds);
    };

    const visibleExistingImages = existingImages.filter(img => !removedImageIds.includes(img.id));
    const totalImages = visibleExistingImages.length + newImages.length;
    const canAddMore = totalImages < maxImages;

    return (
        <div className={`space-y-4 ${className}`}>
            <div className="flex items-center justify-between">
                <Label className="text-base font-medium">
                    Gambar Kost <span className="text-red-500">*</span>
                </Label>
                <span className="text-sm text-muted-foreground">
                    {totalImages}/{maxImages} gambar
                </span>
            </div>

            {/* Upload Area */}
            {canAddMore && (
                <Card className={`border-2 border-dashed transition-colors ${
                    dragActive ? 'border-primary bg-primary/5' : 'border-muted-foreground/25'
                }`}>
                    <CardContent
                        className="flex flex-col items-center justify-center py-8 cursor-pointer"
                        onDragEnter={handleDrag}
                        onDragLeave={handleDrag}
                        onDragOver={handleDrag}
                        onDrop={handleDrop}
                        onClick={() => document.getElementById('image-upload')?.click()}
                    >
                        <Upload className="h-8 w-8 text-muted-foreground mb-2" />
                        <p className="text-sm text-muted-foreground text-center">
                            <span className="font-medium">Klik untuk upload</span> atau drag & drop gambar di sini
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                            PNG, JPG, JPEG, WEBP (max. 2MB per file)
                        </p>
                        <Input
                            id="image-upload"
                            type="file"
                            multiple
                            accept="image/*"
                            onChange={handleFileInput}
                            className="hidden"
                        />
                    </CardContent>
                </Card>
            )}

            {/* Image Preview Grid */}
            {(visibleExistingImages.length > 0 || newImages.length > 0) && (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {/* Existing Images */}
                    {visibleExistingImages.map((image, index) => (
                        <div key={image.id} className="relative group">
                            <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                                <img
                                    src={image.image_url}
                                    alt={image.alt_text || `Gambar ${index + 1}`}
                                    className="w-full h-full object-cover"
                                />
                            </div>
                            {/* Cover Badge */}
                            {image.image_type === 'cover' && (
                                <div className="absolute top-2 left-2">
                                    <div className="bg-primary text-primary-foreground px-2 py-1 rounded text-xs font-medium flex items-center gap-1">
                                        <Star className="h-3 w-3" />
                                        Cover
                                    </div>
                                </div>
                            )}
                            {/* Remove Button */}
                            <Button
                                type="button"
                                variant="destructive"
                                size="sm"
                                className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={() => removeExistingImage(image.id)}
                            >
                                <X className="h-3 w-3" />
                            </Button>
                        </div>
                    ))}

                    {/* New Images */}
                    {newImages.map((file, index) => (
                        <div key={`new-${index}`} className="relative group">
                            <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                                <img
                                    src={URL.createObjectURL(file)}
                                    alt={`Gambar baru ${index + 1}`}
                                    className="w-full h-full object-cover"
                                />
                            </div>
                            {/* New Badge */}
                            <div className="absolute top-2 left-2">
                                <div className="bg-green-500 text-white px-2 py-1 rounded text-xs font-medium">
                                    Baru
                                </div>
                            </div>
                            {/* Remove Button */}
                            <Button
                                type="button"
                                variant="destructive"
                                size="sm"
                                className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={() => removeNewImage(index)}
                            >
                                <X className="h-3 w-3" />
                            </Button>
                        </div>
                    ))}
                </div>
            )}

            {/* Removed Images (for restoration) */}
            {removedImageIds.length > 0 && (
                <div className="space-y-2">
                    <Label className="text-sm font-medium text-muted-foreground">
                        Gambar yang akan dihapus ({removedImageIds.length})
                    </Label>
                    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2">
                        {existingImages
                            .filter(img => removedImageIds.includes(img.id))
                            .map((image) => (
                                <div key={`removed-${image.id}`} className="relative group">
                                    <div className="aspect-square rounded-lg overflow-hidden bg-gray-100 opacity-50">
                                        <img
                                            src={image.image_url}
                                            alt={image.alt_text || 'Gambar'}
                                            className="w-full h-full object-cover"
                                        />
                                    </div>
                                    {/* Restore Button */}
                                    <Button
                                        type="button"
                                        variant="secondary"
                                        size="sm"
                                        className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                        onClick={() => restoreExistingImage(image.id)}
                                    >
                                        Pulihkan
                                    </Button>
                                </div>
                            ))}
                    </div>
                </div>
            )}

            {/* Help Text */}
            <div className="text-sm text-muted-foreground space-y-1">
                <p>• Gambar pertama akan menjadi gambar cover kost</p>
                <p>• Upload minimal 1 gambar untuk kost Anda</p>
                <p>• Gunakan gambar berkualitas baik untuk menarik calon penyewa</p>
            </div>
        </div>
    );
};

export default ImageUpload;
