<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Admin User
        User::create([
            'name' => 'Admin SIM Kost',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => User::ROLE_ADMIN,
            'phone' => '081234567890',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        // Pemilik Kost User
        User::create([
            'name' => 'Budi Pemilik <PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => User::ROLE_PEMILIK_KOST,
            'phone' => '081234567891',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        // Pencari <PERSON>st User
        User::create([
            'name' => '<PERSON><PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => User::ROLE_PENCARI_KOST,
            'phone' => '081234567892',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        // Additional test users
        User::factory(5)->create([
            'role' => User::ROLE_PENCARI_KOST,
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        User::factory(3)->create([
            'role' => User::ROLE_PEMILIK_KOST,
            'is_active' => true,
            'email_verified_at' => now(),
        ]);
    }
}
