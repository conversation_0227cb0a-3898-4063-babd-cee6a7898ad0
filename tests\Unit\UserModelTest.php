<?php

namespace Tests\Unit;

use App\Models\User;
use PHPUnit\Framework\TestCase;

class UserModelTest extends TestCase
{
    public function test_user_role_constants_are_defined(): void
    {
        $this->assertEquals('admin', User::ROLE_ADMIN);
        $this->assertEquals('pemilik_kost', User::ROLE_PEMILIK_KOST);
        $this->assertEquals('pencari_kost', User::ROLE_PENCARI_KOST);
    }

    public function test_get_roles_returns_all_roles(): void
    {
        $roles = User::getRoles();
        
        $this->assertIsArray($roles);
        $this->assertCount(3, $roles);
        $this->assertContains(User::ROLE_ADMIN, $roles);
        $this->assertContains(User::ROLE_PEMILIK_KOST, $roles);
        $this->assertContains(User::ROLE_PENCARI_KOST, $roles);
    }

    public function test_user_has_fillable_attributes(): void
    {
        $user = new User();
        $fillable = $user->getFillable();
        
        $expectedFillable = [
            'name',
            'email',
            'password',
            'role',
            'phone',
            'avatar',
            'is_active',
        ];
        
        foreach ($expectedFillable as $attribute) {
            $this->assertContains($attribute, $fillable);
        }
    }

    public function test_user_has_hidden_attributes(): void
    {
        $user = new User();
        $hidden = $user->getHidden();
        
        $this->assertContains('password', $hidden);
        $this->assertContains('remember_token', $hidden);
    }
}
