<?php

namespace Database\Factories;

use App\Models\Inquiry;
use App\Models\Kost;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Inquiry>
 */
class InquiryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $messages = [
            'Apakah masih ada kamar kosong? Saya tertarik untuk melihat kamar.',
            'Boleh tanya fasilitas apa saja yang tersedia di kost ini?',
            'Saya ingin booking kamar, bagaimana prosedurnya?',
            'Apakah bisa bayar bulanan? Dan berapa deposit yang diperlukan?',
            'Lokasi kost dekat dengan kampus tidak? Transportasi mudah?',
            'Fasilitas WiFi bagaimana? Kencang tidak internetnya?',
            'Apakah ada aturan khusus untuk penghuni kost?',
            'Bisa lihat foto kamar yang tersedia tidak?',
        ];

        return [
            'user_id' => User::factory()->pencariKost(),
            'kost_id' => Kost::factory(),
            'message' => fake()->randomElement($messages),
            'contact_preference' => fake()->randomElement(['email', 'phone', 'whatsapp']),
            'status' => fake()->randomElement(['pending', 'responded', 'closed']),
        ];
    }

    /**
     * Indicate that the inquiry is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
        ]);
    }

    /**
     * Indicate that the inquiry has been responded to.
     */
    public function responded(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'responded',
            'responded_at' => fake()->dateTimeBetween('-1 week', 'now'),
        ]);
    }

    /**
     * Indicate that the inquiry is closed.
     */
    public function closed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'closed',
            'responded_at' => fake()->dateTimeBetween('-2 weeks', '-1 week'),
        ]);
    }
}
