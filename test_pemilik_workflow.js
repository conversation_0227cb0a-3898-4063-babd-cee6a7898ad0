// Test script untuk workflow Pemilik Kost
const BASE_URL = 'http://localhost:8000';

// Helper function untuk HTTP requests
async function makeRequest(url, options = {}) {
    try {
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                ...options.headers
            },
            ...options
        });
        
        const data = await response.json();
        return { status: response.status, data, ok: response.ok };
    } catch (error) {
        console.error('Request failed:', error);
        return { status: 0, data: { error: error.message }, ok: false };
    }
}

// Test data untuk kost baru
const testKostData = {
    name: 'Kost Test Automation',
    description: 'Kost untuk testing automation workflow',
    address: 'Jl. Testing No. 123, Jakarta',
    city: 'Jakarta',
    price_monthly: 1500000,
    price_yearly: 15000000,
    gender_type: 'campur',
    room_count: 10,
    available_rooms: 8,
    facilities: ['wifi', 'ac', 'kamar_mandi_dalam', 'parkir'],
    rules: 'Tidak boleh bawa teman menginap, Jam malam 22:00',
    contact_info: '081234567890'
};

let pemilikToken = '';

async function loginAsPemilik() {
    console.log('\n=== Login as Pemilik Kost ===');
    
    const loginResult = await makeRequest(`${BASE_URL}/api/login`, {
        method: 'POST',
        body: JSON.stringify({
            email: '<EMAIL>',
            password: 'password123'
        })
    });
    
    console.log('Login Status:', loginResult.status);
    if (loginResult.ok && loginResult.data.success) {
        pemilikToken = loginResult.data.data.token;
        console.log('✅ Login successful, token obtained');
        return true;
    } else {
        console.log('❌ Login failed:', loginResult.data);
        return false;
    }
}

async function testGetMyKosts() {
    console.log('\n=== Testing Get My Kosts ===');
    
    const result = await makeRequest(`${BASE_URL}/api/my-kosts`, {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${pemilikToken}`
        }
    });
    
    console.log('Get My Kosts Status:', result.status);
    console.log('My Kosts Count:', result.data?.data?.length || 0);
    
    if (result.data?.data?.length > 0) {
        console.log('First Kost:', JSON.stringify(result.data.data[0], null, 2));
    }
    
    return result;
}

async function testCreateKost() {
    console.log('\n=== Testing Create Kost ===');
    
    // Note: Untuk testing via API, kita perlu endpoint create kost
    // Karena tidak ada endpoint API untuk create kost, kita akan test via web interface
    console.log('⚠️  Create Kost endpoint not available in API');
    console.log('This would typically be tested via web interface');
    console.log('Test data that would be sent:', JSON.stringify(testKostData, null, 2));
    
    return { status: 'skipped', reason: 'No API endpoint available' };
}

async function testInquiryManagement() {
    console.log('\n=== Testing Inquiry Management ===');
    
    const result = await makeRequest(`${BASE_URL}/api/inquiries`, {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${pemilikToken}`
        }
    });
    
    console.log('Get Inquiries Status:', result.status);
    console.log('Inquiries Count:', result.data?.data?.length || 0);
    
    if (result.data?.data?.length > 0) {
        console.log('First Inquiry:', JSON.stringify(result.data.data[0], null, 2));
        
        // Test update inquiry status
        const inquiryId = result.data.data[0].id;
        console.log('\n--- Testing Update Inquiry Status ---');
        
        const updateResult = await makeRequest(`${BASE_URL}/api/inquiries/${inquiryId}/status`, {
            method: 'PATCH',
            headers: {
                'Authorization': `Bearer ${pemilikToken}`
            },
            body: JSON.stringify({
                status: 'responded',
                response_message: 'Terima kasih atas minat Anda. Silakan hubungi saya untuk informasi lebih lanjut.'
            })
        });
        
        console.log('Update Inquiry Status:', updateResult.status);
        console.log('Update Response:', JSON.stringify(updateResult.data, null, 2));
    }
    
    return result;
}

async function testInquiryStats() {
    console.log('\n=== Testing Inquiry Stats ===');
    
    const result = await makeRequest(`${BASE_URL}/api/inquiry-stats`, {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${pemilikToken}`
        }
    });
    
    console.log('Inquiry Stats Status:', result.status);
    console.log('Inquiry Stats:', JSON.stringify(result.data, null, 2));
    
    return result;
}

async function testWebInterfaceAccess() {
    console.log('\n=== Testing Web Interface Access ===');
    
    // Test akses ke halaman pemilik kost
    console.log('🌐 Web interface testing would include:');
    console.log('1. Access to /pemilik/dashboard');
    console.log('2. Access to /pemilik/kosts');
    console.log('3. Access to /pemilik/kosts/create');
    console.log('4. Kost creation form submission');
    console.log('5. Image upload functionality');
    console.log('6. Kost management (edit, delete, status change)');
    
    return { status: 'info', message: 'Web interface testing requires browser automation' };
}

// Main test runner
async function runPemilikWorkflowTests() {
    console.log('🏠 Starting Pemilik Kost Workflow Testing...');
    console.log('Base URL:', BASE_URL);
    
    try {
        // Login as pemilik
        const loginSuccess = await loginAsPemilik();
        if (!loginSuccess) {
            console.log('❌ Cannot proceed without login');
            return;
        }
        
        // Test get my kosts
        await testGetMyKosts();
        
        // Test create kost (limited by API availability)
        await testCreateKost();
        
        // Test inquiry management
        await testInquiryManagement();
        
        // Test inquiry stats
        await testInquiryStats();
        
        // Test web interface access info
        await testWebInterfaceAccess();
        
        console.log('\n=== Pemilik Workflow Test Summary ===');
        console.log('✅ API tests completed!');
        console.log('📝 Note: Full workflow testing requires web interface automation');
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

// Run tests
runPemilikWorkflowTests();
