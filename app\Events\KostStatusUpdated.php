<?php

namespace App\Events;

use App\Models\Kost;
use App\Models\Notification;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class KostStatusUpdated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Kost $kost;
    public Notification $notification;
    public string $oldStatus;

    /**
     * Create a new event instance.
     */
    public function __construct(Kost $kost, Notification $notification, string $oldStatus)
    {
        $this->kost = $kost;
        $this->notification = $notification;
        $this->oldStatus = $oldStatus;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('user.' . $this->kost->owner_id),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'kost.status-updated';
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'kost' => [
                'id' => $this->kost->id,
                'name' => $this->kost->name,
                'status' => $this->kost->status,
                'old_status' => $this->oldStatus,
            ],
            'notification' => [
                'id' => $this->notification->id,
                'title' => $this->notification->title,
                'message' => $this->notification->message,
                'type' => $this->notification->type,
                'created_at' => $this->notification->created_at,
            ],
        ];
    }
}
