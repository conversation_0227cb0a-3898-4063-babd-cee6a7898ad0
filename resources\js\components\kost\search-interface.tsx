import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { AiSearchResult } from '@/types';
import { Loader2, Search, Sparkles, Clock, Target, MessageSquare } from 'lucide-react';
import React, { useState } from 'react';

interface SearchInterfaceProps {
    onSearch: (query: string) => Promise<AiSearchResult>;
    isLoading?: boolean;
    className?: string;
}

const SearchInterface: React.FC<SearchInterfaceProps> = ({
    onSearch,
    isLoading = false,
    className = '',
}) => {
    const [query, setQuery] = useState('');
    const [lastResult, setLastResult] = useState<AiSearchResult | null>(null);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!query.trim() || isLoading) return;

        try {
            const result = await onSearch(query.trim());
            setLastResult(result);
        } catch (error) {
            console.error('Search error:', error);
            setLastResult({
                success: false,
                message: 'Terjadi kesalahan saat mencari. Silakan coba lagi.',
            });
        }
    };

    const handleExampleClick = (exampleQuery: string) => {
        setQuery(exampleQuery);
    };

    const exampleQueries = [
        "Cari kost putra di Jakarta dengan harga di bawah 2 juta",
        "Kost putri dekat kampus dengan WiFi dan AC",
        "Kost bulanan di Bandung yang ada parkir motor",
        "Kost campur harian di Yogyakarta dengan kamar mandi dalam",
        "Kost murah di Surabaya untuk mahasiswa"
    ];

    return (
        <div className={`space-y-6 ${className}`}>
            {/* Main Search Card */}
            <Card className="border-2 border-dashed border-primary/20 bg-gradient-to-br from-primary/5 to-transparent">
                <CardHeader className="text-center">
                    <CardTitle className="flex items-center justify-center gap-2 text-2xl">
                        <Sparkles className="h-6 w-6 text-primary" />
                        Pencarian Kost dengan AI
                    </CardTitle>
                    <p className="text-muted-foreground">
                        Ceritakan kebutuhan kost Anda dengan bahasa natural, AI akan membantu menemukan kost yang tepat
                    </p>
                </CardHeader>
                <CardContent>
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="relative">
                            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                            <Input
                                type="text"
                                placeholder="Contoh: Cari kost putra di Jakarta dengan harga di bawah 2 juta, ada WiFi dan parkir motor..."
                                value={query}
                                onChange={(e) => setQuery(e.target.value)}
                                className="pl-10 pr-4 py-3 text-base"
                                disabled={isLoading}
                            />
                        </div>
                        <Button
                            type="submit"
                            className="w-full"
                            disabled={!query.trim() || isLoading}
                            size="lg"
                        >
                            {isLoading ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Mencari dengan AI...
                                </>
                            ) : (
                                <>
                                    <Sparkles className="mr-2 h-4 w-4" />
                                    Cari dengan AI
                                </>
                            )}
                        </Button>
                    </form>
                </CardContent>
            </Card>

            {/* Example Queries */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-lg">
                        <MessageSquare className="h-5 w-5" />
                        Contoh Pencarian
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid gap-2">
                        {exampleQueries.map((example, index) => (
                            <Button
                                key={index}
                                variant="ghost"
                                className="justify-start h-auto p-3 text-left text-wrap"
                                onClick={() => handleExampleClick(example)}
                                disabled={isLoading}
                            >
                                <div className="flex items-start gap-2">
                                    <Target className="h-4 w-4 mt-0.5 text-muted-foreground flex-shrink-0" />
                                    <span className="text-sm">{example}</span>
                                </div>
                            </Button>
                        ))}
                    </div>
                </CardContent>
            </Card>

            {/* Search Result Summary */}
            {lastResult && (
                <Card className={lastResult.success ? 'border-green-200 bg-green-50/50' : 'border-red-200 bg-red-50/50'}>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2 text-lg">
                            {lastResult.success ? (
                                <div className="h-5 w-5 rounded-full bg-green-500 flex items-center justify-center">
                                    <div className="h-2 w-2 rounded-full bg-white" />
                                </div>
                            ) : (
                                <div className="h-5 w-5 rounded-full bg-red-500 flex items-center justify-center">
                                    <div className="h-2 w-2 rounded-full bg-white" />
                                </div>
                            )}
                            Hasil Pencarian
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <p className={lastResult.success ? 'text-green-700' : 'text-red-700'}>
                            {lastResult.message}
                        </p>

                        {lastResult.success && lastResult.data && (
                            <>
                                <Separator />
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                    <div className="flex items-center gap-2">
                                        <Target className="h-4 w-4 text-muted-foreground" />
                                        <span className="font-medium">Hasil Ditemukan:</span>
                                        <span>{lastResult.data.total_results} kost</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Clock className="h-4 w-4 text-muted-foreground" />
                                        <span className="font-medium">Waktu Pencarian:</span>
                                        <span>{lastResult.data.response_time.toFixed(2)}s</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Sparkles className="h-4 w-4 text-muted-foreground" />
                                        <span className="font-medium">Powered by AI</span>
                                    </div>
                                </div>

                                {lastResult.data.ai_interpretation && (
                                    <>
                                        <Separator />
                                        <div className="space-y-2">
                                            <h4 className="font-medium text-sm">Interpretasi AI:</h4>
                                            <p className="text-sm text-muted-foreground bg-white/50 p-3 rounded-lg border">
                                                {lastResult.data.ai_interpretation}
                                            </p>
                                        </div>
                                    </>
                                )}

                                {lastResult.data.recommendations && lastResult.data.recommendations.length > 0 && (
                                    <>
                                        <Separator />
                                        <div className="space-y-2">
                                            <h4 className="font-medium text-sm">Rekomendasi AI:</h4>
                                            <div className="flex flex-wrap gap-2">
                                                {lastResult.data.recommendations.map((rec, index) => (
                                                    <span
                                                        key={index}
                                                        className="px-2 py-1 bg-primary/10 text-primary text-xs rounded-full"
                                                    >
                                                        {rec}
                                                    </span>
                                                ))}
                                            </div>
                                        </div>
                                    </>
                                )}
                            </>
                        )}
                    </CardContent>
                </Card>
            )}
        </div>
    );
};

export default SearchInterface;
