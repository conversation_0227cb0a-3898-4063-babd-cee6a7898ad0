<?php

namespace Database\Factories;

use App\Models\AiSearchLog;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AiSearchLog>
 */
class AiSearchLogFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $queries = [
            'Cari kost putra di Jakarta dengan harga di bawah 2 juta',
            'Kost putri dekat kampus dengan WiFi dan AC',
            'Kost bulanan di Bandung yang ada parkir motor',
            'Kost campur harian di Yogyakarta dengan kamar mandi dalam',
            'Kost murah di Surabaya untuk mahasiswa',
            'Cari kost dengan fasilitas lengkap di Malang',
            'Kost dekat stasiun dengan harga terjangkau',
            'Kost eksklusif dengan kolam renang',
        ];

        $responses = [
            'Berdasarkan kriteria Anda, saya menemukan beberapa kost yang sesuai...',
            'Pencarian berhasil! Berikut adalah kost-kost yang cocok dengan kebutuhan Anda...',
            '<PERSON>a telah menganalisis permintaan Anda dan menemukan opsi terbaik...',
            'Hasil pencarian menunjukkan beberapa pilihan menarik untuk Anda...',
        ];

        return [
            'user_id' => User::factory()->create(['role' => User::ROLE_PENCARI_KOST])->id,
            'query' => fake()->randomElement($queries),
            'ai_response' => fake()->randomElement($responses),
            'results_count' => fake()->numberBetween(0, 20),
            'response_time' => fake()->randomFloat(3, 0.5, 5.0), // 0.5 to 5.0 seconds
        ];
    }

    /**
     * Indicate that the search returned no results.
     */
    public function noResults(): static
    {
        return $this->state(fn (array $attributes) => [
            'results_count' => 0,
            'ai_response' => 'Maaf, tidak ada kost yang sesuai dengan kriteria pencarian Anda.',
        ]);
    }

    /**
     * Indicate that the search was very fast.
     */
    public function fastResponse(): static
    {
        return $this->state(fn (array $attributes) => [
            'response_time' => fake()->randomFloat(3, 0.1, 1.0),
        ]);
    }

    /**
     * Indicate that the search was slow.
     */
    public function slowResponse(): static
    {
        return $this->state(fn (array $attributes) => [
            'response_time' => fake()->randomFloat(3, 3.0, 10.0),
        ]);
    }

    /**
     * Create search log for specific user.
     */
    public function forUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
        ]);
    }

    /**
     * Create search log with many results.
     */
    public function manyResults(): static
    {
        return $this->state(fn (array $attributes) => [
            'results_count' => fake()->numberBetween(15, 50),
        ]);
    }

    /**
     * Create search log with specific query.
     */
    public function withQuery(string $query): static
    {
        return $this->state(fn (array $attributes) => [
            'query' => $query,
        ]);
    }
}
