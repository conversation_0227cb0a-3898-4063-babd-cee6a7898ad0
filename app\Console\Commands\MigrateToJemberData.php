<?php

namespace App\Console\Commands;

use App\Models\Kost;
use App\Models\KostFacility;
use App\Models\KostImage;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class MigrateToJemberData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'kost:migrate-jember {--force : Force migration without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate existing kost data to real Jember kost data from Mamikos';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🏠 Kost Data Migration to Jember Data');
        $this->info('=====================================');

        // Show current data statistics
        $currentKostCount = Kost::count();
        $currentFacilityCount = KostFacility::count();
        $currentImageCount = KostImage::count();

        $this->info("Current data statistics:");
        $this->info("- Kost entries: {$currentKostCount}");
        $this->info("- Facilities: {$currentFacilityCount}");
        $this->info("- Images: {$currentImageCount}");
        $this->newLine();

        // Show sample of current data
        if ($currentKostCount > 0) {
            $this->info("Sample of current kost data:");
            $sampleKosts = Kost::take(3)->get(['id', 'name', 'city', 'price_monthly']);
            foreach ($sampleKosts as $kost) {
                $this->info("- ID: {$kost->id}, Name: {$kost->name}, City: {$kost->city}, Price: Rp" . number_format($kost->price_monthly));
            }
            $this->newLine();
        }

        // Confirmation
        if (!$this->option('force')) {
            if (!$this->confirm('This will REPLACE ALL existing kost data with real Jember data. Continue?')) {
                $this->info('Migration cancelled.');
                return 0;
            }
        }

        $this->info('Starting migration...');
        $this->newLine();

        try {
            // Step 1: Backup existing data (optional - create backup tables)
            $this->info('📋 Step 1: Creating backup of existing data...');
            $this->createBackupTables();
            $this->info('✅ Backup created successfully');

            // Step 2: Clear existing data
            $this->info('🗑️  Step 2: Clearing existing data...');
            $this->clearExistingData();
            $this->info('✅ Existing data cleared');

            // Step 3: Run Jember seeder
            $this->info('🌱 Step 3: Seeding Jember kost data...');
            $this->call('db:seed', ['--class' => 'JemberKostSeeder']);
            $this->info('✅ Jember data seeded successfully');

            // Step 4: Verify new data
            $this->info('🔍 Step 4: Verifying new data...');
            $this->verifyNewData();

            $this->newLine();
            $this->info('🎉 Migration completed successfully!');
            $this->info('=====================================');

            // Show new data statistics
            $newKostCount = Kost::count();
            $newFacilityCount = KostFacility::count();
            $newImageCount = KostImage::count();

            $this->info("New data statistics:");
            $this->info("- Kost entries: {$newKostCount}");
            $this->info("- Facilities: {$newFacilityCount}");
            $this->info("- Images: {$newImageCount}");
            $this->newLine();

            $this->info("Sample of new Jember kost data:");
            $newSampleKosts = Kost::take(3)->get(['id', 'name', 'city', 'price_monthly']);
            foreach ($newSampleKosts as $kost) {
                $this->info("- ID: {$kost->id}, Name: {$kost->name}, City: {$kost->city}, Price: Rp" . number_format($kost->price_monthly));
            }

        } catch (\Exception $e) {
            $this->error('❌ Migration failed: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * Create backup tables for existing data
     */
    private function createBackupTables()
    {
        $timestamp = now()->format('Y_m_d_His');
        
        // Backup kosts table
        DB::statement("CREATE TABLE kosts_backup_{$timestamp} AS SELECT * FROM kosts");
        
        // Backup kost_facilities table
        DB::statement("CREATE TABLE kost_facilities_backup_{$timestamp} AS SELECT * FROM kost_facilities");
        
        // Backup kost_images table
        DB::statement("CREATE TABLE kost_images_backup_{$timestamp} AS SELECT * FROM kost_images");

        $this->info("   Backup tables created with timestamp: {$timestamp}");
    }

    /**
     * Clear existing data
     */
    private function clearExistingData()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        
        KostImage::truncate();
        $this->info('   - Kost images cleared');
        
        KostFacility::truncate();
        $this->info('   - Kost facilities cleared');
        
        Kost::truncate();
        $this->info('   - Kost entries cleared');
        
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    }

    /**
     * Verify new data integrity
     */
    private function verifyNewData()
    {
        $kostCount = Kost::count();
        $facilityCount = KostFacility::count();
        $imageCount = KostImage::count();

        if ($kostCount === 0) {
            throw new \Exception('No kost data was created');
        }

        if ($facilityCount === 0) {
            throw new \Exception('No facility data was created');
        }

        if ($imageCount === 0) {
            throw new \Exception('No image data was created');
        }

        // Verify all kosts are in Jember
        $nonJemberKosts = Kost::where('city', '!=', 'Jember')->count();
        if ($nonJemberKosts > 0) {
            throw new \Exception("Found {$nonJemberKosts} kosts not in Jember");
        }

        // Verify relationships
        $kostsWithoutFacilities = Kost::doesntHave('facilities')->count();
        if ($kostsWithoutFacilities > 0) {
            throw new \Exception("Found {$kostsWithoutFacilities} kosts without facilities");
        }

        $kostsWithoutImages = Kost::doesntHave('images')->count();
        if ($kostsWithoutImages > 0) {
            throw new \Exception("Found {$kostsWithoutImages} kosts without images");
        }

        $this->info("   ✅ Data integrity verified");
        $this->info("   ✅ All {$kostCount} kosts are in Jember");
        $this->info("   ✅ All kosts have facilities and images");
    }
}
