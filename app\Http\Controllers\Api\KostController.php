<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\KostResource;
use App\Models\Kost;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class KostController extends Controller
{
    public function __construct()
    {
        // Middleware akan diterapkan di routes
    }

    /**
     * Display a listing of kosts with filtering and pagination
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $query = Kost::with(['owner', 'facilities', 'images'])
            ->approved()
            ->active();

        // Filter berdasarkan kota
        if ($request->filled('city')) {
            $query->byCity($request->city);
        }

        // Filter berdasarkan gender type
        if ($request->filled('gender_type')) {
            $query->byGender($request->gender_type);
        }

        // Filter berdasarkan range harga
        if ($request->filled('min_price') || $request->filled('max_price')) {
            $query->byPriceRange($request->min_price, $request->max_price);
        }

        // Filter berdasarkan fasilitas
        if ($request->filled('facilities')) {
            $facilities = is_array($request->facilities) ? $request->facilities : [$request->facilities];
            $query->whereHas('facilities', function ($q) use ($facilities) {
                $q->whereIn('name', $facilities);
            });
        }

        // Search berdasarkan nama atau deskripsi
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('address', 'like', "%{$search}%");
            });
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        if (in_array($sortBy, ['created_at', 'price_monthly', 'name', 'available_rooms'])) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $perPage = min($request->get('per_page', 15), 50); // Max 50 items per page
        $kosts = $query->paginate($perPage);

        return KostResource::collection($kosts);
    }

    /**
     * Display the specified kost
     */
    public function show(Kost $kost): KostResource|JsonResponse
    {
        // Pastikan kost disetujui dan aktif untuk non-owner
        $user = Auth::user();
        if ($kost->owner_id !== $user->id && ($kost->status !== 'approved' || !$kost->is_active)) {
            return response()->json([
                'success' => false,
                'message' => 'Kost tidak ditemukan atau tidak tersedia.',
            ], 404);
        }

        $kost->load(['owner', 'facilities', 'images']);

        return new KostResource($kost);
    }

    /**
     * Get kosts owned by authenticated user (for pemilik kost)
     */
    public function myKosts(Request $request): AnonymousResourceCollection|JsonResponse
    {
        $user = Auth::user();

        if (!$user->isPemilikKost()) {
            return response()->json([
                'success' => false,
                'message' => 'Akses ditolak. Hanya pemilik kost yang dapat mengakses endpoint ini.',
            ], 403);
        }

        $query = $user->kosts()->with(['facilities', 'images']);

        // Filter berdasarkan status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $perPage = min($request->get('per_page', 15), 50);
        $kosts = $query->latest()->paginate($perPage);

        return KostResource::collection($kosts);
    }

    /**
     * Get similar kosts based on location, price, or type
     */
    public function similar(Kost $kost, Request $request): AnonymousResourceCollection
    {
        $limit = min($request->get('limit', 4), 10);

        $similarKosts = Kost::with(['owner', 'facilities', 'images'])
            ->approved()
            ->active()
            ->where('id', '!=', $kost->id)
            ->where(function ($query) use ($kost) {
                $query->where('city', $kost->city)
                      ->orWhere('gender_type', $kost->gender_type)
                      ->orWhereBetween('price_monthly', [
                          $kost->price_monthly * 0.7,
                          $kost->price_monthly * 1.3
                      ]);
            })
            ->limit($limit)
            ->get();

        return KostResource::collection($similarKosts);
    }

    /**
     * Get popular kosts (most inquired)
     */
    public function popular(Request $request): AnonymousResourceCollection
    {
        $limit = min($request->get('limit', 10), 20);

        $popularKosts = Kost::with(['owner', 'facilities', 'images'])
            ->approved()
            ->active()
            ->withCount('inquiries')
            ->orderBy('inquiries_count', 'desc')
            ->limit($limit)
            ->get();

        return KostResource::collection($popularKosts);
    }

    /**
     * Get latest kosts
     */
    public function latest(Request $request): AnonymousResourceCollection
    {
        $limit = min($request->get('limit', 10), 20);

        $latestKosts = Kost::with(['owner', 'facilities', 'images'])
            ->approved()
            ->active()
            ->latest()
            ->limit($limit)
            ->get();

        return KostResource::collection($latestKosts);
    }

    /**
     * Get kosts by city
     */
    public function byCity(string $city, Request $request): AnonymousResourceCollection
    {
        $query = Kost::with(['owner', 'facilities', 'images'])
            ->approved()
            ->active()
            ->byCity($city);

        $perPage = min($request->get('per_page', 15), 50);
        $kosts = $query->latest()->paginate($perPage);

        return KostResource::collection($kosts);
    }

    /**
     * Get available cities
     */
    public function cities(): JsonResponse
    {
        $cities = Kost::approved()
            ->active()
            ->distinct()
            ->pluck('city')
            ->sort()
            ->values();

        return response()->json([
            'success' => true,
            'data' => $cities,
        ]);
    }

    /**
     * Get price statistics
     */
    public function priceStats(): JsonResponse
    {
        $stats = Kost::approved()
            ->active()
            ->selectRaw('
                MIN(price_monthly) as min_price,
                MAX(price_monthly) as max_price,
                AVG(price_monthly) as avg_price,
                COUNT(*) as total_kosts
            ')
            ->first();

        return response()->json([
            'success' => true,
            'data' => [
                'min_price' => (float) $stats->min_price,
                'max_price' => (float) $stats->max_price,
                'avg_price' => round((float) $stats->avg_price, 2),
                'total_kosts' => (int) $stats->total_kosts,
                'price_ranges' => [
                    ['label' => 'Di bawah Rp 1 juta', 'min' => 0, 'max' => 1000000],
                    ['label' => 'Rp 1 - 2 juta', 'min' => 1000000, 'max' => 2000000],
                    ['label' => 'Rp 2 - 3 juta', 'min' => 2000000, 'max' => 3000000],
                    ['label' => 'Rp 3 - 5 juta', 'min' => 3000000, 'max' => 5000000],
                    ['label' => 'Di atas Rp 5 juta', 'min' => 5000000, 'max' => null],
                ],
            ],
        ]);
    }
}
