<?php

namespace App\Console\Commands;

use App\Models\AiSearchLog;
use App\Models\Inquiry;
use App\Models\Kost;
use App\Models\KostFacility;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class GenerateTestData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'kost:generate-test-data {--fresh : Drop all data and start fresh}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate test data for SIM Kost application';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if ($this->option('fresh')) {
            if (!$this->confirm('This will delete all existing data. Are you sure?')) {
                $this->info('Operation cancelled.');
                return Command::SUCCESS;
            }

            $this->info('🗑️  Clearing existing data...');
            $this->clearData();
        }

        $this->info('🎲 Generating test data...');

        try {
            DB::beginTransaction();

            // Generate Users
            $this->info('👥 Creating users...');
            $this->createUsers();

            // Generate Kosts
            $this->info('🏠 Creating kosts...');
            $this->createKosts();

            // Generate Inquiries
            $this->info('💬 Creating inquiries...');
            $this->createInquiries();

            // Generate AI Search Logs
            $this->info('🤖 Creating AI search logs...');
            $this->createAiSearchLogs();

            DB::commit();

            $this->info('🎉 Test data generated successfully!');
            $this->displaySummary();

        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('❌ Error generating test data: ' . $e->getMessage());
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }

    private function clearData(): void
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        AiSearchLog::truncate();
        Inquiry::truncate();
        KostFacility::truncate();
        Kost::truncate();
        User::where('email', '!=', '<EMAIL>')->delete();

        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    }

    private function createUsers(): void
    {
        // Create pencari kost users
        User::factory()->count(20)->create([
            'role' => User::ROLE_PENCARI_KOST,
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        // Create pemilik kost users
        User::factory()->count(10)->create([
            'role' => User::ROLE_PEMILIK_KOST,
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        $this->info('   ✅ Created 30 users (20 pencari, 10 pemilik)');
    }

    private function createKosts(): void
    {
        $owners = User::byRole(User::ROLE_PEMILIK_KOST)->get();

        foreach ($owners as $owner) {
            // Each owner has 1-3 kosts
            $kostCount = rand(1, 3);

            for ($i = 0; $i < $kostCount; $i++) {
                $kost = Kost::factory()->create([
                    'owner_id' => $owner->id,
                    'status' => $this->getRandomStatus(),
                ]);

                // Add facilities to each kost
                $this->addFacilitiesToKost($kost);
            }
        }

        $this->info('   ✅ Created kosts with facilities');
    }

    private function createInquiries(): void
    {
        $pencariUsers = User::byRole(User::ROLE_PENCARI_KOST)->get();
        $approvedKosts = Kost::approved()->active()->get();

        if ($approvedKosts->isEmpty()) {
            $this->warn('   ⚠️  No approved kosts found, skipping inquiry creation');
            return;
        }

        foreach ($pencariUsers as $user) {
            // Each pencari sends 0-3 inquiries
            $inquiryCount = rand(0, 3);

            for ($i = 0; $i < $inquiryCount; $i++) {
                $kost = $approvedKosts->random();

                Inquiry::factory()->create([
                    'user_id' => $user->id,
                    'kost_id' => $kost->id,
                ]);
            }
        }

        $this->info('   ✅ Created inquiries');
    }

    private function createAiSearchLogs(): void
    {
        $pencariUsers = User::byRole(User::ROLE_PENCARI_KOST)->get();

        foreach ($pencariUsers as $user) {
            // Each user has 0-5 search logs
            $searchCount = rand(0, 5);

            AiSearchLog::factory()->count($searchCount)->create([
                'user_id' => $user->id,
            ]);
        }

        $this->info('   ✅ Created AI search logs');
    }

    private function getRandomStatus(): string
    {
        $statuses = ['draft', 'pending', 'approved', 'rejected'];
        $weights = [10, 20, 60, 10]; // 60% approved, 20% pending, etc.

        $rand = rand(1, 100);
        $cumulative = 0;

        foreach ($weights as $index => $weight) {
            $cumulative += $weight;
            if ($rand <= $cumulative) {
                return $statuses[$index];
            }
        }

        return 'approved';
    }

    private function addFacilitiesToKost(Kost $kost): void
    {
        $facilities = [
            ['name' => 'WiFi', 'category' => 'umum'],
            ['name' => 'AC', 'category' => 'kamar'],
            ['name' => 'Kamar Mandi Dalam', 'category' => 'kamar_mandi'],
            ['name' => 'Parkir Motor', 'category' => 'parkir'],
            ['name' => 'CCTV', 'category' => 'keamanan'],
            ['name' => 'Dapur Bersama', 'category' => 'umum'],
            ['name' => 'Laundry', 'category' => 'umum'],
            ['name' => 'Kasur', 'category' => 'kamar'],
            ['name' => 'Lemari', 'category' => 'kamar'],
            ['name' => 'Meja Belajar', 'category' => 'kamar'],
        ];

        // Add 3-7 random facilities
        $selectedFacilities = collect($facilities)->random(rand(3, 7));

        foreach ($selectedFacilities as $facility) {
            KostFacility::create([
                'kost_id' => $kost->id,
                'name' => $facility['name'],
                'category' => $facility['category'],
            ]);
        }
    }

    private function displaySummary(): void
    {
        $this->info('📊 Data Summary:');
        $this->table(
            ['Model', 'Count'],
            [
                ['Users', User::count()],
                ['Kosts', Kost::count()],
                ['Facilities', KostFacility::count()],
                ['Inquiries', Inquiry::count()],
                ['AI Search Logs', AiSearchLog::count()],
            ]
        );
    }
}
