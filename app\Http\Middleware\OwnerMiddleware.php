<?php

namespace App\Http\Middleware;

use App\Models\Kost;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class OwnerMiddleware
{
    /**
     * Handle an incoming request.
     * Middleware ini memastikan bahwa user hanya bisa mengakses kost yang mereka miliki
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();

        // Pastikan user sudah login dan adalah pemilik kost
        if (!$user || !$user->isPemilikKost()) {
            return redirect()->route('dashboard')->with('error', 'Aks<PERSON> ditolak.');
        }

        // Jika ada parameter kost_id di route, pastikan kost tersebut milik user
        $kostId = $request->route('kost') ?? $request->route('kost_id');

        if ($kostId) {
            $kost = $kostId instanceof Kost ? $kostId : Kost::find($kostId);

            if (!$kost || $kost->owner_id !== $user->id) {
                return redirect()->route('pemilik.dashboard')->with('error', 'Anda tidak memiliki akses ke kost tersebut.');
            }
        }

        return $next($request);
    }
}
