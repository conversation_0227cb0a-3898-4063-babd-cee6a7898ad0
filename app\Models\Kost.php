<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Kost extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'owner_id',
        'name',
        'description',
        'address',
        'city',
        'province',
        'postal_code',
        'latitude',
        'longitude',
        'price_monthly',
        'price_daily',
        'room_count',
        'available_rooms',
        'gender_type',
        'kost_type',
        'status',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected function casts(): array
    {
        return [
            'latitude' => 'decimal:8',
            'longitude' => 'decimal:8',
            'price_monthly' => 'decimal:2',
            'price_daily' => 'decimal:2',
            'room_count' => 'integer',
            'available_rooms' => 'integer',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Konstanta untuk status kost
     */
    public const STATUS_DRAFT = 'draft';
    public const STATUS_PENDING = 'pending';
    public const STATUS_APPROVED = 'approved';
    public const STATUS_REJECTED = 'rejected';

    /**
     * Konstanta untuk gender type
     */
    public const GENDER_PUTRA = 'putra';
    public const GENDER_PUTRI = 'putri';
    public const GENDER_CAMPUR = 'campur';

    /**
     * Konstanta untuk kost type
     */
    public const TYPE_BULANAN = 'bulanan';
    public const TYPE_HARIAN = 'harian';
    public const TYPE_KEDUANYA = 'keduanya';

    /**
     * Mendapatkan semua status yang tersedia
     */
    public static function getStatuses(): array
    {
        return [
            self::STATUS_DRAFT,
            self::STATUS_PENDING,
            self::STATUS_APPROVED,
            self::STATUS_REJECTED,
        ];
    }

    /**
     * Mendapatkan semua gender type yang tersedia
     */
    public static function getGenderTypes(): array
    {
        return [
            self::GENDER_PUTRA,
            self::GENDER_PUTRI,
            self::GENDER_CAMPUR,
        ];
    }

    /**
     * Mendapatkan semua kost type yang tersedia
     */
    public static function getKostTypes(): array
    {
        return [
            self::TYPE_BULANAN,
            self::TYPE_HARIAN,
            self::TYPE_KEDUANYA,
        ];
    }

    /**
     * Relationship: Kost dimiliki oleh satu user (pemilik)
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    /**
     * Relationship: Kost memiliki banyak fasilitas
     */
    public function facilities(): HasMany
    {
        return $this->hasMany(KostFacility::class);
    }

    /**
     * Relationship: Kost memiliki banyak gambar
     */
    public function images(): HasMany
    {
        return $this->hasMany(KostImage::class);
    }

    /**
     * Relationship: Kost memiliki banyak inquiry
     */
    public function inquiries(): HasMany
    {
        return $this->hasMany(Inquiry::class);
    }

    /**
     * Scope: Filter kost yang disetujui
     */
    public function scopeApproved($query)
    {
        return $query->where('status', self::STATUS_APPROVED);
    }

    /**
     * Scope: Filter kost yang aktif
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope: Filter berdasarkan kota
     */
    public function scopeByCity($query, string $city)
    {
        return $query->where('city', 'like', "%{$city}%");
    }

    /**
     * Scope: Filter berdasarkan gender type
     */
    public function scopeByGender($query, string $gender)
    {
        return $query->where('gender_type', $gender);
    }

    /**
     * Scope: Filter berdasarkan range harga
     */
    public function scopeByPriceRange($query, ?float $minPrice = null, ?float $maxPrice = null)
    {
        if ($minPrice !== null) {
            $query->where('price_monthly', '>=', $minPrice);
        }

        if ($maxPrice !== null) {
            $query->where('price_monthly', '<=', $maxPrice);
        }

        return $query;
    }

    /**
     * Cek apakah kost masih tersedia
     */
    public function isAvailable(): bool
    {
        return $this->available_rooms > 0 && $this->is_active && $this->status === self::STATUS_APPROVED;
    }

    /**
     * Mendapatkan gambar cover kost
     */
    public function getCoverImage()
    {
        return $this->images()->where('image_type', 'cover')->first();
    }

    /**
     * Mendapatkan harga yang diformat
     */
    public function getFormattedPriceAttribute(): string
    {
        return 'Rp ' . number_format($this->price_monthly, 0, ',', '.');
    }
}
