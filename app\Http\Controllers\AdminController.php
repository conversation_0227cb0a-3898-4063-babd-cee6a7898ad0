<?php

namespace App\Http\Controllers;

use App\Models\AiSearchLog;
use App\Models\Inquiry;
use App\Models\Kost;
use App\Models\Notification;
use App\Models\User;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class AdminController extends Controller
{
    private NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Dashboard utama admin
     */
    public function dashboard()
    {
        // Statistik umum
        $stats = [
            'total_users' => User::count(),
            'total_pencari' => User::byRole(User::ROLE_PENCARI_KOST)->count(),
            'total_pemilik' => User::byRole(User::ROLE_PEMILIK_KOST)->count(),
            'total_kosts' => Kost::count(),
            'pending_kosts' => Kost::where('status', 'pending')->count(),
            'approved_kosts' => Kost::approved()->count(),
            'total_inquiries' => Inquiry::count(),
            'total_ai_searches' => AiSearchLog::count(),
        ];

        // Kost yang perlu direview
        $pendingKosts = Kost::with(['owner', 'images'])
            ->where('status', 'pending')
            ->latest()
            ->limit(5)
            ->get();

        // User terbaru
        $recentUsers = User::latest()
            ->limit(5)
            ->get();

        // Aktivitas AI search terbaru
        $recentAiSearches = AiSearchLog::with('user')
            ->latest()
            ->limit(5)
            ->get();

        // Statistik bulanan
        $monthlyStats = [
            'users_this_month' => User::whereMonth('created_at', now()->month)->count(),
            'kosts_this_month' => Kost::whereMonth('created_at', now()->month)->count(),
            'inquiries_this_month' => Inquiry::whereMonth('created_at', now()->month)->count(),
            'ai_searches_this_month' => AiSearchLog::whereMonth('created_at', now()->month)->count(),
        ];

        return Inertia::render('Admin/Dashboard', [
            'stats' => $stats,
            'monthlyStats' => $monthlyStats,
            'pendingKosts' => $pendingKosts,
            'recentUsers' => $recentUsers,
            'recentAiSearches' => $recentAiSearches,
        ]);
    }

    /**
     * Manajemen user
     */
    public function users(Request $request)
    {
        $query = User::query();

        // Filter berdasarkan role
        if ($request->filled('role')) {
            $query->byRole($request->role);
        }

        // Filter berdasarkan status aktif
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Search berdasarkan nama atau email
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $users = $query->latest()->paginate(15)->withQueryString();

        return Inertia::render('Admin/Users', [
            'users' => $users,
            'filters' => $request->only(['role', 'status', 'search']),
        ]);
    }

    /**
     * Toggle status aktif user
     */
    public function toggleUserStatus(User $user)
    {
        // Pastikan tidak menonaktifkan admin
        if ($user->isAdmin()) {
            return redirect()->back()->with('error', 'Tidak dapat mengubah status admin.');
        }

        $user->update(['is_active' => !$user->is_active]);

        $status = $user->is_active ? 'diaktifkan' : 'dinonaktifkan';
        return redirect()->back()->with('success', "User {$user->name} berhasil {$status}.");
    }

    /**
     * Manajemen kost
     */
    public function kosts(Request $request)
    {
        $query = Kost::with(['owner', 'images']);

        // Filter berdasarkan status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter berdasarkan kota
        if ($request->filled('city')) {
            $query->byCity($request->city);
        }

        // Search berdasarkan nama kost atau pemilik
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('owner', function ($ownerQuery) use ($search) {
                      $ownerQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        $kosts = $query->latest()->paginate(15)->withQueryString();

        // Data untuk filter
        $cities = Kost::distinct()->pluck('city')->sort()->values();

        return Inertia::render('Admin/Kosts', [
            'kosts' => $kosts,
            'filters' => $request->only(['status', 'city', 'search']),
            'cities' => $cities,
        ]);
    }

    /**
     * Detail kost untuk review
     */
    public function kostDetail(Kost $kost)
    {
        $kost->load(['owner', 'facilities', 'images', 'inquiries.user']);

        return Inertia::render('Admin/KostDetail', [
            'kost' => $kost,
        ]);
    }

    /**
     * Approve kost
     */
    public function approveKost(Kost $kost)
    {
        if ($kost->status !== 'pending') {
            return redirect()->back()->with('error', 'Hanya kost dengan status pending yang dapat disetujui.');
        }

        $oldStatus = $kost->status;
        $kost->update(['status' => 'approved']);

        // Kirim notifikasi real-time ke pemilik kost
        $this->notificationService->sendKostStatusNotification($kost, $oldStatus);

        return redirect()->back()->with('success', 'Kost berhasil disetujui.');
    }

    /**
     * Reject kost
     */
    public function rejectKost(Request $request, Kost $kost)
    {
        if ($kost->status !== 'pending') {
            return redirect()->back()->with('error', 'Hanya kost dengan status pending yang dapat ditolak.');
        }

        $reason = $request->input('reason', '');
        $oldStatus = $kost->status;
        $kost->update(['status' => 'rejected']);

        // Kirim notifikasi real-time ke pemilik kost
        $this->notificationService->sendKostStatusNotification($kost, $oldStatus);

        return redirect()->back()->with('success', 'Kost berhasil ditolak.');
    }

    /**
     * Monitoring aktivitas AI search
     */
    public function aiSearchLogs(Request $request)
    {
        $query = AiSearchLog::with('user');

        // Filter berdasarkan user
        if ($request->filled('user_id')) {
            $query->byUser($request->user_id);
        }

        // Filter berdasarkan tanggal
        if ($request->filled('date_from') && $request->filled('date_to')) {
            $query->byDateRange($request->date_from, $request->date_to);
        }

        $logs = $query->latest()->paginate(20)->withQueryString();

        // Statistik AI search
        $aiStats = [
            'total_searches' => AiSearchLog::count(),
            'unique_users' => AiSearchLog::distinct('user_id')->count('user_id'),
            'avg_response_time' => AiSearchLog::avg('response_time'),
            'total_results_found' => AiSearchLog::sum('results_count'),
        ];

        return Inertia::render('Admin/AiSearchLogs', [
            'logs' => $logs,
            'filters' => $request->only(['user_id', 'date_from', 'date_to']),
            'aiStats' => $aiStats,
        ]);
    }

    /**
     * Laporan dan statistik
     */
    public function reports()
    {
        // Statistik per bulan (6 bulan terakhir)
        $monthlyData = [];
        for ($i = 5; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $monthlyData[] = [
                'month' => $date->format('M Y'),
                'users' => User::whereYear('created_at', $date->year)
                    ->whereMonth('created_at', $date->month)
                    ->count(),
                'kosts' => Kost::whereYear('created_at', $date->year)
                    ->whereMonth('created_at', $date->month)
                    ->count(),
                'inquiries' => Inquiry::whereYear('created_at', $date->year)
                    ->whereMonth('created_at', $date->month)
                    ->count(),
                'ai_searches' => AiSearchLog::whereYear('created_at', $date->year)
                    ->whereMonth('created_at', $date->month)
                    ->count(),
            ];
        }

        // Top cities
        $topCities = Kost::select('city', DB::raw('count(*) as total'))
            ->groupBy('city')
            ->orderBy('total', 'desc')
            ->limit(10)
            ->get();

        // User distribution by role
        $userDistribution = [
            'pencari_kost' => User::byRole(User::ROLE_PENCARI_KOST)->count(),
            'pemilik_kost' => User::byRole(User::ROLE_PEMILIK_KOST)->count(),
            'admin' => User::byRole(User::ROLE_ADMIN)->count(),
        ];

        return Inertia::render('Admin/Reports', [
            'monthlyData' => $monthlyData,
            'topCities' => $topCities,
            'userDistribution' => $userDistribution,
        ]);
    }
}
