<?php

namespace Database\Factories;

use App\Models\Kost;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Kost>
 */
class KostFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $cities = ['Jakarta', 'Bandung', 'Surabaya', 'Yogyakarta', 'Semarang', 'Malang', 'Solo', 'Denpasar'];
        $provinces = ['DKI Jakarta', 'Jawa Barat', 'Jawa Timur', 'DI Yogyakarta', 'Jawa Tengah', 'Bali'];

        return [
            'owner_id' => User::factory()->create(['role' => User::ROLE_PEMILIK_KOST])->id,
            'name' => 'Kost ' . fake()->company(),
            'description' => fake()->paragraph(3),
            'address' => fake()->address(),
            'city' => fake()->randomElement($cities),
            'province' => fake()->randomElement($provinces),
            'postal_code' => fake()->postcode(),
            'latitude' => fake()->latitude(-8, -6), // Indonesia latitude range
            'longitude' => fake()->longitude(106, 115), // Indonesia longitude range
            'price_monthly' => fake()->numberBetween(800000, 5000000),
            'price_daily' => fake()->numberBetween(30000, 200000),
            'room_count' => fake()->numberBetween(5, 50),
            'available_rooms' => fake()->numberBetween(1, 10),
            'gender_type' => fake()->randomElement(['putra', 'putri', 'campur']),
            'kost_type' => fake()->randomElement(['bulanan', 'harian', 'keduanya']),
            'status' => 'approved',
            'is_active' => true,
        ];
    }

    /**
     * Indicate that the kost is in draft status.
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'draft',
        ]);
    }

    /**
     * Indicate that the kost is pending approval.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
        ]);
    }

    /**
     * Indicate that the kost is rejected.
     */
    public function rejected(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'rejected',
        ]);
    }

    /**
     * Indicate that the kost is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the kost is for male only.
     */
    public function putra(): static
    {
        return $this->state(fn (array $attributes) => [
            'gender_type' => 'putra',
        ]);
    }

    /**
     * Indicate that the kost is for female only.
     */
    public function putri(): static
    {
        return $this->state(fn (array $attributes) => [
            'gender_type' => 'putri',
        ]);
    }

    /**
     * Indicate that the kost is mixed gender.
     */
    public function campur(): static
    {
        return $this->state(fn (array $attributes) => [
            'gender_type' => 'campur',
        ]);
    }

    /**
     * Indicate that the kost is monthly only.
     */
    public function bulanan(): static
    {
        return $this->state(fn (array $attributes) => [
            'kost_type' => 'bulanan',
            'price_daily' => null,
        ]);
    }

    /**
     * Indicate that the kost is daily only.
     */
    public function harian(): static
    {
        return $this->state(fn (array $attributes) => [
            'kost_type' => 'harian',
        ]);
    }

    /**
     * Create kost with specific city.
     */
    public function inCity(string $city): static
    {
        return $this->state(fn (array $attributes) => [
            'city' => $city,
        ]);
    }

    /**
     * Create kost with specific price range.
     */
    public function priceRange(int $min, int $max): static
    {
        return $this->state(fn (array $attributes) => [
            'price_monthly' => fake()->numberBetween($min, $max),
        ]);
    }
}
