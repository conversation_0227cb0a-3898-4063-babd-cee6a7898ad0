<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class KostFacility extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'kost_id',
        'name',
        'description',
        'icon',
        'category',
    ];

    /**
     * Konstanta untuk kategori fasilitas
     */
    public const CATEGORY_KAMAR = 'kamar';
    public const CATEGORY_KAMAR_MANDI = 'kamar_mandi';
    public const CATEGORY_UMUM = 'umum';
    public const CATEGORY_KEAMANAN = 'keamanan';
    public const CATEGORY_PARKIR = 'parkir';

    /**
     * Mendapatkan semua kategori yang tersedia
     */
    public static function getCategories(): array
    {
        return [
            self::CATEGORY_KAMAR => 'Fasilitas Kamar',
            self::CATEGORY_KAMAR_MANDI => 'Fasilitas Kamar Mandi',
            self::CATEGORY_UMUM => 'Fasilitas Umum',
            self::CATEGORY_KEAMANAN => 'Keamanan',
            self::CATEGORY_PARKIR => 'Parkir',
        ];
    }

    /**
     * Relationship: Fasilitas milik satu kost
     */
    public function kost(): BelongsTo
    {
        return $this->belongsTo(Kost::class);
    }

    /**
     * Scope: Filter berdasarkan kategori
     */
    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Mendapatkan nama kategori yang diformat
     */
    public function getCategoryNameAttribute(): string
    {
        $categories = self::getCategories();
        return $categories[$this->category] ?? $this->category;
    }
}
