import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Kost } from '@/types';
import { 
    MapPin, 
    Users, 
    Calendar, 
    Bed, 
    Eye, 
    MessageCircle,
    Star,
    Wifi,
    Car,
    Shield,
    Bath
} from 'lucide-react';
import React from 'react';

interface KostCardProps {
    kost: Kost;
    onView?: (kost: Kost) => void;
    onInquiry?: (kost: Kost) => void;
    showOwnerInfo?: boolean;
    className?: string;
}

const KostCard: React.FC<KostCardProps> = ({
    kost,
    onView,
    onInquiry,
    showOwnerInfo = false,
    className = '',
}) => {
    const coverImage = kost.images?.find(img => img.image_type === 'cover') || kost.images?.[0];
    
    const getGenderBadgeColor = (gender: string) => {
        switch (gender) {
            case 'putra': return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
            case 'putri': return 'bg-pink-100 text-pink-800 hover:bg-pink-200';
            case 'campur': return 'bg-purple-100 text-purple-800 hover:bg-purple-200';
            default: return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
        }
    };

    const getStatusBadgeColor = (status: string) => {
        switch (status) {
            case 'approved': return 'bg-green-100 text-green-800 hover:bg-green-200';
            case 'pending': return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
            case 'rejected': return 'bg-red-100 text-red-800 hover:bg-red-200';
            case 'draft': return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
            default: return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
        }
    };

    const getFacilityIcon = (category: string) => {
        switch (category) {
            case 'kamar': return <Bed className="h-4 w-4" />;
            case 'kamar_mandi': return <Bath className="h-4 w-4" />;
            case 'umum': return <Wifi className="h-4 w-4" />;
            case 'keamanan': return <Shield className="h-4 w-4" />;
            case 'parkir': return <Car className="h-4 w-4" />;
            default: return <Star className="h-4 w-4" />;
        }
    };

    return (
        <Card className={`overflow-hidden transition-all duration-200 hover:shadow-lg ${className}`}>
            {/* Cover Image */}
            <div className="relative h-48 overflow-hidden">
                {coverImage ? (
                    <img
                        src={coverImage.image_url}
                        alt={coverImage.alt_text || kost.name}
                        className="h-full w-full object-cover transition-transform duration-200 hover:scale-105"
                    />
                ) : (
                    <div className="flex h-full w-full items-center justify-center bg-gray-100">
                        <Bed className="h-12 w-12 text-gray-400" />
                    </div>
                )}
                
                {/* Status Badge */}
                <div className="absolute top-2 right-2">
                    <Badge className={getStatusBadgeColor(kost.status)}>
                        {kost.status === 'approved' ? 'Tersedia' : 
                         kost.status === 'pending' ? 'Menunggu' :
                         kost.status === 'rejected' ? 'Ditolak' : 'Draft'}
                    </Badge>
                </div>

                {/* Available Rooms Badge */}
                {kost.available_rooms > 0 && (
                    <div className="absolute top-2 left-2">
                        <Badge variant="secondary" className="bg-white/90 text-gray-800">
                            {kost.available_rooms} kamar tersisa
                        </Badge>
                    </div>
                )}
            </div>

            <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                    <div className="flex-1">
                        <h3 className="font-semibold text-lg leading-tight line-clamp-2">
                            {kost.name}
                        </h3>
                        <div className="flex items-center gap-1 mt-1 text-sm text-muted-foreground">
                            <MapPin className="h-4 w-4" />
                            <span className="line-clamp-1">{kost.city}, {kost.province}</span>
                        </div>
                    </div>
                    <div className="text-right">
                        <div className="text-lg font-bold text-primary">
                            {kost.formatted_price}
                        </div>
                        <div className="text-xs text-muted-foreground">per bulan</div>
                    </div>
                </div>
            </CardHeader>

            <CardContent className="space-y-3">
                {/* Gender and Type Badges */}
                <div className="flex gap-2">
                    <Badge className={getGenderBadgeColor(kost.gender_type)}>
                        <Users className="h-3 w-3 mr-1" />
                        {kost.gender_type === 'putra' ? 'Putra' :
                         kost.gender_type === 'putri' ? 'Putri' : 'Campur'}
                    </Badge>
                    <Badge variant="outline">
                        <Calendar className="h-3 w-3 mr-1" />
                        {kost.kost_type === 'bulanan' ? 'Bulanan' :
                         kost.kost_type === 'harian' ? 'Harian' : 'Bulanan/Harian'}
                    </Badge>
                </div>

                {/* Description */}
                <p className="text-sm text-muted-foreground line-clamp-2">
                    {kost.description}
                </p>

                {/* Facilities */}
                {kost.facilities && kost.facilities.length > 0 && (
                    <div className="space-y-2">
                        <Separator />
                        <div className="flex flex-wrap gap-2">
                            {kost.facilities.slice(0, 4).map((facility) => (
                                <div
                                    key={facility.id}
                                    className="flex items-center gap-1 text-xs text-muted-foreground"
                                >
                                    {getFacilityIcon(facility.category)}
                                    <span>{facility.name}</span>
                                </div>
                            ))}
                            {kost.facilities.length > 4 && (
                                <span className="text-xs text-muted-foreground">
                                    +{kost.facilities.length - 4} lainnya
                                </span>
                            )}
                        </div>
                    </div>
                )}

                {/* Owner Info */}
                {showOwnerInfo && kost.owner && (
                    <div className="space-y-2">
                        <Separator />
                        <div className="flex items-center gap-2 text-sm">
                            <div className="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center">
                                <span className="text-xs font-medium text-primary">
                                    {kost.owner.name.charAt(0).toUpperCase()}
                                </span>
                            </div>
                            <span className="text-muted-foreground">
                                Pemilik: {kost.owner.name}
                            </span>
                        </div>
                    </div>
                )}
            </CardContent>

            <CardFooter className="pt-3 gap-2">
                <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={() => onView?.(kost)}
                >
                    <Eye className="h-4 w-4 mr-2" />
                    Lihat Detail
                </Button>
                {kost.status === 'approved' && kost.available_rooms > 0 && (
                    <Button
                        size="sm"
                        className="flex-1"
                        onClick={() => onInquiry?.(kost)}
                    >
                        <MessageCircle className="h-4 w-4 mr-2" />
                        Tanya Kost
                    </Button>
                )}
            </CardFooter>
        </Card>
    );
};

export default KostCard;
