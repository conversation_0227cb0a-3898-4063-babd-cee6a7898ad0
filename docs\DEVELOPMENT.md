# 🛠️ SIM Kost Development Guide

## Development Environment Setup

### Prerequisites
- PHP 8.2+
- Node.js 18+
- MySQL 8.0+
- Composer
- Git

### Quick Start
```bash
# Clone repository
git clone https://github.com/your-username/sim-kost.git
cd sim-kost

# Install dependencies
composer install
npm install

# Setup environment
cp .env.example .env
php artisan key:generate

# Setup database
php artisan migrate --seed

# Start development servers
php artisan serve
npm run dev
```

## Project Structure

```
sim-kost/
├── app/
│   ├── Console/Commands/          # Artisan commands
│   ├── Events/                    # Laravel events
│   ├── Http/
│   │   ├── Controllers/           # Controllers
│   │   │   ├── Api/              # API controllers
│   │   │   └── Web/              # Web controllers
│   │   ├── Middleware/           # Custom middleware
│   │   └── Resources/            # API resources
│   ├── Models/                   # Eloquent models
│   └── Services/                 # Business logic services
├── database/
│   ├── factories/                # Model factories
│   ├── migrations/               # Database migrations
│   └── seeders/                  # Database seeders
├── resources/
│   ├── js/
│   │   ├── components/           # React components
│   │   │   ├── ui/              # UI components (shadcn/ui)
│   │   │   └── kost/            # Kost-specific components
│   │   ├── layouts/             # Layout components
│   │   ├── pages/               # Page components
│   │   └── types/               # TypeScript types
│   └── views/                   # Blade templates
├── routes/
│   ├── api.php                  # API routes
│   └── web.php                  # Web routes
└── tests/
    ├── Feature/                 # Feature tests
    └── Unit/                    # Unit tests
```

## Architecture Overview

### Backend Architecture
- **Laravel 11**: Main framework
- **Service Layer**: Business logic separation
- **Repository Pattern**: Data access abstraction
- **Event-Driven**: Real-time notifications
- **API Resources**: Consistent API responses

### Frontend Architecture
- **React 18**: UI library
- **TypeScript**: Type safety
- **Inertia.js**: SPA without API complexity
- **Component-Based**: Reusable UI components
- **State Management**: React hooks + Inertia

### Database Design
- **Users**: Multi-role authentication
- **Kosts**: Property listings
- **Inquiries**: Communication system
- **Notifications**: Real-time alerts
- **AI Search Logs**: Search analytics

## Development Workflow

### Git Workflow
```bash
# Create feature branch
git checkout -b feature/new-feature

# Make changes and commit
git add .
git commit -m "feat: add new feature"

# Push and create PR
git push origin feature/new-feature
```

### Commit Message Convention
```
feat: add new feature
fix: bug fix
docs: documentation update
style: formatting changes
refactor: code refactoring
test: add tests
chore: maintenance tasks
```

### Code Style Guidelines

#### PHP (Laravel)
- Follow PSR-12 coding standards
- Use type hints and return types
- Write descriptive method names
- Keep controllers thin, services fat

```php
// Good
public function createKost(CreateKostRequest $request): Kost
{
    return $this->kostService->create($request->validated());
}

// Bad
public function create($request)
{
    // Complex logic in controller
}
```

#### TypeScript/React
- Use functional components with hooks
- Implement proper TypeScript types
- Follow React best practices
- Use consistent naming conventions

```tsx
// Good
interface KostCardProps {
    kost: Kost;
    onView: (kost: Kost) => void;
}

const KostCard: React.FC<KostCardProps> = ({ kost, onView }) => {
    // Component logic
};

// Bad
const KostCard = (props: any) => {
    // Component logic
};
```

## Testing Strategy

### Backend Testing
```bash
# Run all tests
php artisan test

# Run specific test
php artisan test --filter KostApiTest

# Run with coverage
php artisan test --coverage
```

### Test Structure
```php
class KostApiTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_create_kost(): void
    {
        $user = User::factory()->create(['role' => 'pemilik_kost']);
        Sanctum::actingAs($user);

        $response = $this->postJson('/api/kosts', [
            'name' => 'Test Kost',
            'city' => 'Jakarta',
            // ... other data
        ]);

        $response->assertStatus(201)
                ->assertJsonStructure(['data' => ['id', 'name']]);
    }
}
```

### Frontend Testing
```bash
# Run Jest tests
npm test

# Run with coverage
npm run test:coverage

# Run E2E tests (if configured)
npm run test:e2e
```

## API Development

### Creating New Endpoints
1. Create controller method
2. Add route in `routes/api.php`
3. Create API resource (if needed)
4. Add validation rules
5. Write tests

### API Response Format
```php
// Success response
return response()->json([
    'success' => true,
    'message' => 'Operation successful',
    'data' => $data,
]);

// Error response
return response()->json([
    'success' => false,
    'message' => 'Error message',
    'errors' => $errors,
], 422);
```

## Database Development

### Creating Migrations
```bash
# Create migration
php artisan make:migration create_kosts_table

# Create model with migration
php artisan make:model Kost -m

# Run migrations
php artisan migrate

# Rollback migrations
php artisan migrate:rollback
```

### Migration Best Practices
```php
public function up()
{
    Schema::create('kosts', function (Blueprint $table) {
        $table->id();
        $table->foreignId('owner_id')->constrained('users');
        $table->string('name');
        $table->text('description');
        $table->decimal('price_monthly', 10, 2);
        $table->enum('status', ['draft', 'pending', 'approved', 'rejected']);
        $table->boolean('is_active')->default(true);
        $table->timestamps();
        
        $table->index(['status', 'is_active']);
        $table->index('city');
    });
}
```

### Model Relationships
```php
class Kost extends Model
{
    protected $fillable = [
        'owner_id', 'name', 'description', 'price_monthly'
    ];

    protected $casts = [
        'price_monthly' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function facilities(): HasMany
    {
        return $this->hasMany(KostFacility::class);
    }
}
```

## Frontend Development

### Component Development
```tsx
// Create reusable components
interface ButtonProps {
    variant?: 'primary' | 'secondary';
    size?: 'sm' | 'md' | 'lg';
    children: React.ReactNode;
    onClick?: () => void;
}

const Button: React.FC<ButtonProps> = ({
    variant = 'primary',
    size = 'md',
    children,
    onClick,
}) => {
    const baseClasses = 'font-medium rounded-lg transition-colors';
    const variantClasses = {
        primary: 'bg-blue-600 text-white hover:bg-blue-700',
        secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300',
    };
    const sizeClasses = {
        sm: 'px-3 py-1.5 text-sm',
        md: 'px-4 py-2 text-base',
        lg: 'px-6 py-3 text-lg',
    };

    return (
        <button
            className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]}`}
            onClick={onClick}
        >
            {children}
        </button>
    );
};
```

### State Management
```tsx
// Use React hooks for state management
const useKostSearch = () => {
    const [kosts, setKosts] = useState<Kost[]>([]);
    const [loading, setLoading] = useState(false);
    const [filters, setFilters] = useState<SearchFilters>({});

    const searchKosts = useCallback(async (newFilters: SearchFilters) => {
        setLoading(true);
        try {
            const response = await fetch('/api/kosts', {
                method: 'GET',
                // ... request logic
            });
            const data = await response.json();
            setKosts(data.data);
        } catch (error) {
            console.error('Search error:', error);
        } finally {
            setLoading(false);
        }
    }, []);

    return { kosts, loading, filters, setFilters, searchKosts };
};
```

## Performance Optimization

### Backend Optimization
```php
// Use eager loading
$kosts = Kost::with(['owner', 'facilities', 'images'])
    ->approved()
    ->paginate(15);

// Cache expensive queries
$popularKosts = Cache::remember('popular_kosts', 3600, function () {
    return Kost::withCount('inquiries')
        ->orderBy('inquiries_count', 'desc')
        ->limit(10)
        ->get();
});

// Use database indexes
Schema::table('kosts', function (Blueprint $table) {
    $table->index(['status', 'is_active']);
    $table->index('city');
});
```

### Frontend Optimization
```tsx
// Use React.memo for expensive components
const KostCard = React.memo<KostCardProps>(({ kost, onView }) => {
    // Component logic
});

// Lazy load components
const KostDetail = React.lazy(() => import('./KostDetail'));

// Use useMemo for expensive calculations
const filteredKosts = useMemo(() => {
    return kosts.filter(kost => 
        kost.city.toLowerCase().includes(searchTerm.toLowerCase())
    );
}, [kosts, searchTerm]);
```

## Debugging

### Backend Debugging
```php
// Use Laravel debugbar (development only)
composer require barryvdh/laravel-debugbar --dev

// Log debugging information
Log::info('User searched for kosts', [
    'user_id' => $user->id,
    'query' => $query,
    'results_count' => $results->count(),
]);

// Use dd() for quick debugging
dd($variable);
```

### Frontend Debugging
```tsx
// Use React Developer Tools
// Console logging
console.log('Component rendered with props:', props);

// Error boundaries
class ErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError(error) {
        return { hasError: true };
    }

    componentDidCatch(error, errorInfo) {
        console.error('Error caught by boundary:', error, errorInfo);
    }

    render() {
        if (this.state.hasError) {
            return <h1>Something went wrong.</h1>;
        }

        return this.props.children;
    }
}
```

## Useful Commands

### Laravel Commands
```bash
# Generate test data
php artisan kost:generate-test-data

# Cleanup unused images
php artisan kost:cleanup-images

# Send reminders
php artisan kost:send-reminders

# Clear all caches
php artisan optimize:clear

# Run queue worker
php artisan queue:work
```

### Development Tools
```bash
# Watch for file changes
npm run dev

# Build for production
npm run build

# Run linting
npm run lint

# Format code
npm run format
```

## Contributing Guidelines

1. **Fork the repository**
2. **Create feature branch** from `develop`
3. **Write tests** for new features
4. **Follow code style** guidelines
5. **Update documentation** if needed
6. **Submit pull request** with clear description

### Pull Request Template
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Tests pass locally
- [ ] New tests added
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
```

## Resources

- [Laravel Documentation](https://laravel.com/docs)
- [React Documentation](https://react.dev)
- [TypeScript Handbook](https://www.typescriptlang.org/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Inertia.js Documentation](https://inertiajs.com)

---

Happy coding! 🚀
