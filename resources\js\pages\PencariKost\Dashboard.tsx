import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import KostCard from '@/components/kost/kost-card';
import SearchInterface from '@/components/kost/search-interface';
import AppLayout from '@/layouts/app-layout';
import { AiSearchResult, Inquiry, Kost, Notification, PageProps } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { 
    Search, 
    MessageCircle, 
    Bell, 
    Home, 
    TrendingUp,
    Clock,
    CheckCircle,
    AlertCircle,
    Eye,
    Sparkles
} from 'lucide-react';
import React, { useState } from 'react';

interface DashboardProps extends PageProps {
    stats: {
        total_inquiries: number;
        pending_inquiries: number;
        responded_inquiries: number;
        unread_notifications: number;
    };
    latestKosts: Kost[];
    recentInquiries: Inquiry[];
    recentNotifications: Notification[];
    aiServiceAvailable: boolean;
}

export default function Dashboard({
    auth,
    stats,
    latestKosts,
    recentInquiries,
    recentNotifications,
    aiServiceAvailable,
}: DashboardProps) {
    const [searchResults, setSearchResults] = useState<Kost[]>([]);
    const [isSearching, setIsSearching] = useState(false);

    const handleAiSearch = async (query: string): Promise<AiSearchResult> => {
        setIsSearching(true);
        try {
            const response = await fetch('/api/ai-search/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({ query }),
            });

            const result = await response.json();
            
            if (result.success && result.data?.results) {
                setSearchResults(result.data.results);
            }
            
            return result;
        } catch (error) {
            console.error('AI Search error:', error);
            return {
                success: false,
                message: 'Terjadi kesalahan saat mencari. Silakan coba lagi.',
            };
        } finally {
            setIsSearching(false);
        }
    };

    const handleViewKost = (kost: Kost) => {
        router.visit(route('pencari.kost.show', kost.id));
    };

    const handleInquiryKost = (kost: Kost) => {
        router.visit(route('pencari.kost.show', kost.id), {
            data: { showInquiry: true },
        });
    };

    const getInquiryStatusColor = (status: string) => {
        switch (status) {
            case 'pending': return 'bg-yellow-100 text-yellow-800';
            case 'responded': return 'bg-green-100 text-green-800';
            case 'closed': return 'bg-gray-100 text-gray-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getInquiryStatusIcon = (status: string) => {
        switch (status) {
            case 'pending': return <Clock className="h-4 w-4" />;
            case 'responded': return <CheckCircle className="h-4 w-4" />;
            case 'closed': return <AlertCircle className="h-4 w-4" />;
            default: return <Clock className="h-4 w-4" />;
        }
    };

    return (
        <AppLayout>
            <Head title="Dashboard Pencari Kost" />

            <div className="space-y-8">
                {/* Welcome Section */}
                <div className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg p-6">
                    <h1 className="text-2xl font-bold mb-2">
                        Selamat datang, {auth.user.name}! 👋
                    </h1>
                    <p className="text-muted-foreground">
                        Temukan kost impian Anda dengan bantuan AI atau jelajahi kost-kost terbaru yang tersedia.
                    </p>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Inquiry</CardTitle>
                            <MessageCircle className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_inquiries}</div>
                            <p className="text-xs text-muted-foreground">
                                Inquiry yang pernah dikirim
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Menunggu Respon</CardTitle>
                            <Clock className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.pending_inquiries}</div>
                            <p className="text-xs text-muted-foreground">
                                Inquiry belum direspon
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Sudah Direspon</CardTitle>
                            <CheckCircle className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.responded_inquiries}</div>
                            <p className="text-xs text-muted-foreground">
                                Inquiry sudah direspon
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Notifikasi Baru</CardTitle>
                            <Bell className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.unread_notifications}</div>
                            <p className="text-xs text-muted-foreground">
                                Notifikasi belum dibaca
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* AI Search Section */}
                {aiServiceAvailable && (
                    <div className="space-y-6">
                        <SearchInterface
                            onSearch={handleAiSearch}
                            isLoading={isSearching}
                        />

                        {/* AI Search Results */}
                        {searchResults.length > 0 && (
                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <h2 className="text-xl font-semibold flex items-center gap-2">
                                        <Sparkles className="h-5 w-5 text-primary" />
                                        Hasil Pencarian AI
                                    </h2>
                                    <Badge variant="secondary">
                                        {searchResults.length} kost ditemukan
                                    </Badge>
                                </div>
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                    {searchResults.map((kost) => (
                                        <KostCard
                                            key={kost.id}
                                            kost={kost}
                                            onView={handleViewKost}
                                            onInquiry={handleInquiryKost}
                                        />
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>
                )}

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Latest Kosts */}
                    <div className="lg:col-span-2 space-y-6">
                        <div className="flex items-center justify-between">
                            <h2 className="text-xl font-semibold flex items-center gap-2">
                                <TrendingUp className="h-5 w-5" />
                                Kost Terbaru
                            </h2>
                            <Button variant="outline" asChild>
                                <Link href={route('pencari.search')}>
                                    <Search className="h-4 w-4 mr-2" />
                                    Lihat Semua
                                </Link>
                            </Button>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {latestKosts.map((kost) => (
                                <KostCard
                                    key={kost.id}
                                    kost={kost}
                                    onView={handleViewKost}
                                    onInquiry={handleInquiryKost}
                                />
                            ))}
                        </div>

                        {latestKosts.length === 0 && (
                            <Card>
                                <CardContent className="flex items-center justify-center py-8">
                                    <div className="text-center text-muted-foreground">
                                        <Home className="h-8 w-8 mx-auto mb-2 opacity-50" />
                                        <p>Belum ada kost yang tersedia</p>
                                    </div>
                                </CardContent>
                            </Card>
                        )}
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        {/* Recent Inquiries */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <MessageCircle className="h-5 w-5" />
                                    Inquiry Terbaru
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {recentInquiries.length > 0 ? (
                                    <>
                                        {recentInquiries.map((inquiry) => (
                                            <div key={inquiry.id} className="space-y-2">
                                                <div className="flex items-start justify-between">
                                                    <div className="flex-1 min-w-0">
                                                        <p className="font-medium text-sm truncate">
                                                            {inquiry.kost?.name}
                                                        </p>
                                                        <p className="text-xs text-muted-foreground">
                                                            {new Date(inquiry.created_at).toLocaleDateString('id-ID')}
                                                        </p>
                                                    </div>
                                                    <Badge className={getInquiryStatusColor(inquiry.status)}>
                                                        <div className="flex items-center gap-1">
                                                            {getInquiryStatusIcon(inquiry.status)}
                                                            <span className="text-xs">{inquiry.status_name}</span>
                                                        </div>
                                                    </Badge>
                                                </div>
                                                <p className="text-sm text-muted-foreground line-clamp-2">
                                                    {inquiry.message}
                                                </p>
                                            </div>
                                        ))}
                                        <Separator />
                                        <Button variant="outline" size="sm" className="w-full" asChild>
                                            <Link href={route('pencari.inquiries')}>
                                                Lihat Semua Inquiry
                                            </Link>
                                        </Button>
                                    </>
                                ) : (
                                    <div className="text-center text-muted-foreground py-4">
                                        <MessageCircle className="h-6 w-6 mx-auto mb-2 opacity-50" />
                                        <p className="text-sm">Belum ada inquiry</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Recent Notifications */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Bell className="h-5 w-5" />
                                    Notifikasi Terbaru
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {recentNotifications.length > 0 ? (
                                    <>
                                        {recentNotifications.map((notification) => (
                                            <div key={notification.id} className="space-y-1">
                                                <div className="flex items-start justify-between">
                                                    <p className="font-medium text-sm">{notification.title}</p>
                                                    {!notification.read_at && (
                                                        <div className="h-2 w-2 bg-primary rounded-full flex-shrink-0 mt-1" />
                                                    )}
                                                </div>
                                                <p className="text-sm text-muted-foreground line-clamp-2">
                                                    {notification.message}
                                                </p>
                                                <p className="text-xs text-muted-foreground">
                                                    {new Date(notification.created_at).toLocaleDateString('id-ID')}
                                                </p>
                                            </div>
                                        ))}
                                        <Separator />
                                        <Button variant="outline" size="sm" className="w-full" asChild>
                                            <Link href={route('pencari.notifications')}>
                                                Lihat Semua Notifikasi
                                            </Link>
                                        </Button>
                                    </>
                                ) : (
                                    <div className="text-center text-muted-foreground py-4">
                                        <Bell className="h-6 w-6 mx-auto mb-2 opacity-50" />
                                        <p className="text-sm">Belum ada notifikasi</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
