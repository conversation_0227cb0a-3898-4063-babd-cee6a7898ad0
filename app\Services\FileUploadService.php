<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

class FileUploadService
{
    /**
     * Upload dan optimasi gambar kost
     */
    public function uploadKostImage(UploadedFile $file, string $kostName = ''): array
    {
        try {
            // Validasi file
            $this->validateImageFile($file);
            
            // Generate nama file yang unik
            $filename = $this->generateUniqueFilename($file, $kostName);
            
            // Path untuk menyimpan file
            $path = 'kost-images/' . $filename;
            
            // Optimasi gambar
            $optimizedImage = $this->optimizeImage($file);
            
            // Simpan ke storage
            Storage::disk('public')->put($path, $optimizedImage);
            
            // Generate thumbnail
            $thumbnailPath = $this->generateThumbnail($file, $filename);
            
            return [
                'success' => true,
                'path' => $path,
                'thumbnail_path' => $thumbnailPath,
                'url' => Storage::disk('public')->url($path),
                'thumbnail_url' => Storage::disk('public')->url($thumbnailPath),
                'filename' => $filename,
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Upload multiple images
     */
    public function uploadMultipleKostImages(array $files, string $kostName = ''): array
    {
        $results = [];
        $successCount = 0;
        $errors = [];

        foreach ($files as $index => $file) {
            $result = $this->uploadKostImage($file, $kostName . '_' . ($index + 1));
            
            if ($result['success']) {
                $results[] = $result;
                $successCount++;
            } else {
                $errors[] = "File " . ($index + 1) . ": " . $result['error'];
            }
        }

        return [
            'success' => $successCount > 0,
            'uploaded_count' => $successCount,
            'total_files' => count($files),
            'results' => $results,
            'errors' => $errors,
        ];
    }

    /**
     * Hapus gambar dari storage
     */
    public function deleteKostImage(string $path): bool
    {
        try {
            // Hapus file utama
            if (Storage::disk('public')->exists($path)) {
                Storage::disk('public')->delete($path);
            }
            
            // Hapus thumbnail jika ada
            $thumbnailPath = $this->getThumbnailPath($path);
            if (Storage::disk('public')->exists($thumbnailPath)) {
                Storage::disk('public')->delete($thumbnailPath);
            }
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Validasi file gambar
     */
    private function validateImageFile(UploadedFile $file): void
    {
        // Validasi tipe file
        $allowedMimes = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp'];
        if (!in_array($file->getMimeType(), $allowedMimes)) {
            throw new \InvalidArgumentException('Tipe file tidak didukung. Gunakan JPEG, PNG, JPG, atau WEBP.');
        }

        // Validasi ukuran file (max 2MB)
        $maxSize = 2 * 1024 * 1024; // 2MB in bytes
        if ($file->getSize() > $maxSize) {
            throw new \InvalidArgumentException('Ukuran file terlalu besar. Maksimal 2MB.');
        }

        // Validasi dimensi gambar
        $imageInfo = getimagesize($file->getPathname());
        if (!$imageInfo) {
            throw new \InvalidArgumentException('File bukan gambar yang valid.');
        }

        $width = $imageInfo[0];
        $height = $imageInfo[1];

        // Minimal 300x300 pixel
        if ($width < 300 || $height < 300) {
            throw new \InvalidArgumentException('Dimensi gambar terlalu kecil. Minimal 300x300 pixel.');
        }

        // Maksimal 4000x4000 pixel
        if ($width > 4000 || $height > 4000) {
            throw new \InvalidArgumentException('Dimensi gambar terlalu besar. Maksimal 4000x4000 pixel.');
        }
    }

    /**
     * Generate nama file yang unik
     */
    private function generateUniqueFilename(UploadedFile $file, string $prefix = ''): string
    {
        $extension = $file->getClientOriginalExtension();
        $timestamp = now()->format('YmdHis');
        $random = Str::random(8);
        
        $cleanPrefix = $prefix ? Str::slug($prefix) . '_' : '';
        
        return $cleanPrefix . $timestamp . '_' . $random . '.' . $extension;
    }

    /**
     * Optimasi gambar untuk web
     */
    private function optimizeImage(UploadedFile $file): string
    {
        // Baca gambar
        $image = Image::make($file->getPathname());
        
        // Resize jika terlalu besar (max width 1200px)
        if ($image->width() > 1200) {
            $image->resize(1200, null, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            });
        }
        
        // Compress berdasarkan format
        $quality = 85; // Default quality
        $format = strtolower($file->getClientOriginalExtension());
        
        switch ($format) {
            case 'jpg':
            case 'jpeg':
                return $image->encode('jpg', $quality)->__toString();
            case 'png':
                return $image->encode('png', 9)->__toString(); // PNG compression level 0-9
            case 'webp':
                return $image->encode('webp', $quality)->__toString();
            default:
                return $image->encode('jpg', $quality)->__toString();
        }
    }

    /**
     * Generate thumbnail
     */
    private function generateThumbnail(UploadedFile $file, string $filename): string
    {
        $image = Image::make($file->getPathname());
        
        // Resize ke 300x300 dengan crop
        $thumbnail = $image->fit(300, 300);
        
        // Path untuk thumbnail
        $thumbnailFilename = 'thumb_' . $filename;
        $thumbnailPath = 'kost-images/thumbnails/' . $thumbnailFilename;
        
        // Simpan thumbnail
        Storage::disk('public')->put($thumbnailPath, $thumbnail->encode('jpg', 80)->__toString());
        
        return $thumbnailPath;
    }

    /**
     * Get thumbnail path dari path gambar utama
     */
    private function getThumbnailPath(string $imagePath): string
    {
        $filename = basename($imagePath);
        return 'kost-images/thumbnails/thumb_' . $filename;
    }

    /**
     * Get informasi gambar
     */
    public function getImageInfo(string $path): array
    {
        if (!Storage::disk('public')->exists($path)) {
            return [
                'exists' => false,
                'error' => 'File tidak ditemukan.',
            ];
        }

        $fullPath = Storage::disk('public')->path($path);
        $imageInfo = getimagesize($fullPath);
        
        if (!$imageInfo) {
            return [
                'exists' => true,
                'error' => 'Bukan file gambar yang valid.',
            ];
        }

        return [
            'exists' => true,
            'width' => $imageInfo[0],
            'height' => $imageInfo[1],
            'mime_type' => $imageInfo['mime'],
            'size' => Storage::disk('public')->size($path),
            'url' => Storage::disk('public')->url($path),
            'thumbnail_url' => Storage::disk('public')->exists($this->getThumbnailPath($path)) 
                ? Storage::disk('public')->url($this->getThumbnailPath($path)) 
                : null,
        ];
    }

    /**
     * Bersihkan file yang tidak terpakai
     */
    public function cleanupUnusedImages(): array
    {
        $deletedCount = 0;
        $errors = [];

        try {
            // Get semua file gambar di storage
            $allImages = Storage::disk('public')->files('kost-images');
            
            // Get semua path gambar yang digunakan di database
            $usedImages = \App\Models\KostImage::pluck('image_path')->toArray();
            
            // Hapus file yang tidak digunakan
            foreach ($allImages as $imagePath) {
                if (!in_array($imagePath, $usedImages)) {
                    if (Storage::disk('public')->delete($imagePath)) {
                        $deletedCount++;
                    } else {
                        $errors[] = "Gagal menghapus: " . $imagePath;
                    }
                }
            }
            
            return [
                'success' => true,
                'deleted_count' => $deletedCount,
                'errors' => $errors,
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }
}
