<?php

namespace Database\Seeders;

use App\Models\Kost;
use App\Models\KostFacility;
use App\Models\KostImage;
use App\Models\User;
use Illuminate\Database\Seeder;

class AdditionalJemberKostSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * Add 56 more entries to reach exactly 500 kost data
     */
    public function run(): void
    {
        $this->command->info('Adding 56 more kost entries to reach 500 total...');
        
        // Get existing owners
        $owners = User::where('role', 'pemilik_kost')->get();
        
        if ($owners->isEmpty()) {
            $this->command->error('No owners found! Please run EnhancedJemberKostSeeder first.');
            return;
        }

        // Generate 56 additional premium kost entries
        $additionalKostData = $this->generateAdditionalKostData();
        
        $this->command->info('Seeding additional kost entries...');
        $this->seedKostEntries($additionalKostData, $owners);
        
        $totalKost = Kost::count();
        $this->command->info("Additional kost data seeded successfully! Total now: {$totalKost} entries");
    }

    private function generateAdditionalKostData(): array
    {
        $kostData = [];
        
        // Premium kost areas in Jember
        $premiumAreas = [
            ['area' => 'Sumbersari', 'postal' => '68121', 'lat_base' => -8.1650, 'lng_base' => 113.7080],
            ['area' => 'Kaliwates', 'postal' => '68131', 'lat_base' => -8.1720, 'lng_base' => 113.6980],
            ['area' => 'Patrang', 'postal' => '68118', 'lat_base' => -8.1580, 'lng_base' => 113.7150],
            ['area' => 'Wuluhan', 'postal' => '68162', 'lat_base' => -8.1200, 'lng_base' => 113.7500],
            ['area' => 'Arjasa', 'postal' => '68191', 'lat_base' => -8.0950, 'lng_base' => 113.7200],
            ['area' => 'Balung', 'postal' => '68161', 'lat_base' => -8.2950, 'lng_base' => 113.9200],
        ];

        $kostTypes = ['Residence', 'Villa', 'Griya', 'Wisma', 'Pondok', 'House', 'Executive', 'Premium'];
        $genderTypes = ['putra', 'putri', 'campur'];
        
        // Generate 56 premium entries
        for ($i = 1; $i <= 56; $i++) {
            $area = $premiumAreas[array_rand($premiumAreas)];
            $kostType = $kostTypes[array_rand($kostTypes)];
            $genderType = $genderTypes[array_rand($genderTypes)];
            
            // Premium pricing (800k - 2.5M)
            $basePrice = rand(800000, 2500000);
            $roomCount = rand(10, 25);
            $availableRooms = rand(0, min(4, $roomCount));
            
            // Generate coordinates within area
            $latitude = $area['lat_base'] + (rand(-100, 100) / 10000);
            $longitude = $area['lng_base'] + (rand(-100, 100) / 10000);
            
            $kostData[] = [
                'name' => "Kost {$kostType} {$area['area']} Premium {$i}",
                'description' => "Kost {$genderType} premium eksklusif di {$area['area']} dengan fasilitas mewah dan lokasi strategis. Dilengkapi dengan berbagai amenitas modern untuk kenyamanan maksimal.",
                'address' => "Jl. {$area['area']} Premium No. {$i}, {$area['area']}",
                'city' => 'Jember',
                'province' => 'Jawa Timur',
                'postal_code' => $area['postal'],
                'latitude' => $latitude,
                'longitude' => $longitude,
                'price_monthly' => $basePrice,
                'price_daily' => null,
                'room_count' => $roomCount,
                'available_rooms' => $availableRooms,
                'gender_type' => $genderType,
                'kost_type' => 'bulanan',
                'status' => 'approved',
                'is_active' => true,
                'facilities' => $this->generatePremiumFacilities($basePrice),
            ];
        }
        
        return $kostData;
    }

    private function generatePremiumFacilities(int $price): array
    {
        // Base premium facilities
        $facilities = [
            ['name' => 'Kasur', 'category' => 'kamar'],
            ['name' => 'Lemari', 'category' => 'kamar'],
            ['name' => 'Meja Belajar', 'category' => 'kamar'],
            ['name' => 'Kursi', 'category' => 'kamar'],
            ['name' => 'AC', 'category' => 'kamar'],
            ['name' => 'Kamar Mandi Dalam', 'category' => 'kamar_mandi'],
            ['name' => 'Kloset Duduk', 'category' => 'kamar_mandi'],
            ['name' => 'Water Heater', 'category' => 'kamar_mandi'],
            ['name' => 'WiFi', 'category' => 'umum'],
            ['name' => 'Dapur Bersama', 'category' => 'umum'],
            ['name' => 'Akses 24 Jam', 'category' => 'keamanan'],
            ['name' => 'CCTV', 'category' => 'keamanan'],
            ['name' => 'Parkir Motor', 'category' => 'parkir'],
        ];

        // Super premium facilities for high-end kost
        if ($price >= 1500000) {
            $superPremiumFacilities = [
                ['name' => 'TV', 'category' => 'kamar'],
                ['name' => 'Kulkas Mini', 'category' => 'kamar'],
                ['name' => 'Balkon', 'category' => 'kamar'],
                ['name' => 'Bathtub', 'category' => 'kamar_mandi'],
                ['name' => 'Ruang Tamu', 'category' => 'umum'],
                ['name' => 'Gym', 'category' => 'umum'],
                ['name' => 'Laundry', 'category' => 'umum'],
                ['name' => 'Security 24 Jam', 'category' => 'keamanan'],
                ['name' => 'Parkir Mobil', 'category' => 'parkir'],
            ];
            $facilities = array_merge($facilities, $superPremiumFacilities);
        }

        // Executive facilities for ultra-premium kost
        if ($price >= 2000000) {
            $executiveFacilities = [
                ['name' => 'Smart TV', 'category' => 'kamar'],
                ['name' => 'Microwave', 'category' => 'kamar'],
                ['name' => 'Coffee Maker', 'category' => 'kamar'],
                ['name' => 'Jacuzzi', 'category' => 'kamar_mandi'],
                ['name' => 'Rooftop', 'category' => 'umum'],
                ['name' => 'Swimming Pool', 'category' => 'umum'],
                ['name' => 'Concierge', 'category' => 'umum'],
                ['name' => 'Valet Parking', 'category' => 'parkir'],
            ];
            $facilities = array_merge($facilities, $executiveFacilities);
        }

        return $facilities;
    }

    private function seedKostEntries(array $kostData, $owners): void
    {
        foreach ($kostData as $index => $data) {
            $facilities = $data['facilities'];
            unset($data['facilities']);
            
            // Assign random owner
            $data['owner_id'] = $owners->random()->id;
            $kost = Kost::create($data);

            // Create facilities
            foreach ($facilities as $facility) {
                KostFacility::create([
                    'kost_id' => $kost->id,
                    'name' => $facility['name'],
                    'category' => $facility['category'],
                    'description' => null,
                    'icon' => null,
                ]);
            }

            // Create sample images
            $imageTypes = ['cover', 'room', 'facility', 'exterior'];
            foreach ($imageTypes as $imageIndex => $type) {
                KostImage::create([
                    'kost_id' => $kost->id,
                    'image_path' => "kost-images/{$kost->id}/{$type}.jpg",
                    'image_type' => $type,
                    'alt_text' => "{$kost->name} - {$type}",
                    'sort_order' => $imageIndex + 1,
                ]);
            }
            
            if (($index + 1) % 10 === 0) {
                $this->command->info("Added " . ($index + 1) . " additional entries...");
            }
        }
    }
}
