<?php

namespace App\Events;

use App\Models\Inquiry;
use App\Models\Notification;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class NewInquiryNotification implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Inquiry $inquiry;
    public Notification $notification;

    /**
     * Create a new event instance.
     */
    public function __construct(Inquiry $inquiry, Notification $notification)
    {
        $this->inquiry = $inquiry;
        $this->notification = $notification;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('user.' . $this->inquiry->kost->owner_id),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'inquiry.new';
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'inquiry' => [
                'id' => $this->inquiry->id,
                'message' => $this->inquiry->message,
                'contact_preference' => $this->inquiry->contact_preference,
                'status' => $this->inquiry->status,
                'created_at' => $this->inquiry->created_at,
                'user' => [
                    'id' => $this->inquiry->user->id,
                    'name' => $this->inquiry->user->name,
                    'email' => $this->inquiry->user->email,
                ],
                'kost' => [
                    'id' => $this->inquiry->kost->id,
                    'name' => $this->inquiry->kost->name,
                    'city' => $this->inquiry->kost->city,
                ],
            ],
            'notification' => [
                'id' => $this->notification->id,
                'title' => $this->notification->title,
                'message' => $this->notification->message,
                'type' => $this->notification->type,
                'created_at' => $this->notification->created_at,
            ],
        ];
    }
}
