<?php

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        $user = Auth::user();

        // Redirect berdasarkan role
        return match ($user->role) {
            'pencari_kost' => redirect()->route('pencari.dashboard'),
            'pemilik_kost' => redirect()->route('pemilik.dashboard'),
            'admin' => redirect()->route('admin.dashboard'),
            default => Inertia::render('dashboard'),
        };
    })->name('dashboard');
});

// Routes untuk Pencari Kost
Route::middleware(['auth', 'verified', 'role:pencari_kost'])->prefix('pencari')->name('pencari.')->group(function () {
    Route::get('dashboard', [App\Http\Controllers\PencariKostController::class, 'dashboard'])->name('dashboard');
    Route::get('search', [App\Http\Controllers\PencariKostController::class, 'search'])->name('search');
    Route::get('kost/{kost}', [App\Http\Controllers\PencariKostController::class, 'show'])->name('kost.show');
    Route::get('inquiries', [App\Http\Controllers\PencariKostController::class, 'inquiries'])->name('inquiries');
    Route::get('notifications', [App\Http\Controllers\PencariKostController::class, 'notifications'])->name('notifications');
});

// Routes untuk Pemilik Kost
Route::middleware(['auth', 'verified', 'role:pemilik_kost'])->prefix('pemilik')->name('pemilik.')->group(function () {
    Route::get('dashboard', [App\Http\Controllers\PemilikKostController::class, 'dashboard'])->name('dashboard');
    Route::get('kosts', [App\Http\Controllers\PemilikKostController::class, 'index'])->name('kosts.index');
    Route::get('kosts/create', [App\Http\Controllers\PemilikKostController::class, 'create'])->name('kosts.create');
    Route::post('kosts', [App\Http\Controllers\PemilikKostController::class, 'store'])->name('kosts.store');
    Route::get('kosts/{kost}', [App\Http\Controllers\PemilikKostController::class, 'show'])->name('kosts.show')->middleware('owner');
    Route::get('kosts/{kost}/edit', [App\Http\Controllers\PemilikKostController::class, 'edit'])->name('kosts.edit')->middleware('owner');
    Route::put('kosts/{kost}', [App\Http\Controllers\PemilikKostController::class, 'update'])->name('kosts.update')->middleware('owner');
    Route::post('kosts/{kost}/submit', [App\Http\Controllers\PemilikKostController::class, 'submit'])->name('kosts.submit')->middleware('owner');
    Route::delete('kosts/{kost}', [App\Http\Controllers\PemilikKostController::class, 'destroy'])->name('kosts.destroy')->middleware('owner');
    Route::get('inquiries', [App\Http\Controllers\PemilikKostController::class, 'inquiries'])->name('inquiries');
    Route::get('notifications', [App\Http\Controllers\PemilikKostController::class, 'notifications'])->name('notifications');
});

// Routes untuk Admin
Route::middleware(['auth', 'verified', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('dashboard', [App\Http\Controllers\AdminController::class, 'dashboard'])->name('dashboard');
    Route::get('users', [App\Http\Controllers\AdminController::class, 'users'])->name('users');
    Route::patch('users/{user}/toggle-status', [App\Http\Controllers\AdminController::class, 'toggleUserStatus'])->name('users.toggle-status');
    Route::get('kosts', [App\Http\Controllers\AdminController::class, 'kosts'])->name('kosts');
    Route::get('kosts/{kost}', [App\Http\Controllers\AdminController::class, 'kostDetail'])->name('kosts.detail');
    Route::post('kosts/{kost}/approve', [App\Http\Controllers\AdminController::class, 'approveKost'])->name('kosts.approve');
    Route::post('kosts/{kost}/reject', [App\Http\Controllers\AdminController::class, 'rejectKost'])->name('kosts.reject');
    Route::get('ai-search-logs', [App\Http\Controllers\AdminController::class, 'aiSearchLogs'])->name('ai-search-logs');
    Route::get('reports', [App\Http\Controllers\AdminController::class, 'reports'])->name('reports');
});

// Routes untuk Inquiry
Route::middleware(['auth', 'verified'])->group(function () {
    Route::post('kost/{kost}/inquiry', [App\Http\Controllers\InquiryController::class, 'store'])->name('inquiries.store');
    Route::get('inquiry/{inquiry}', [App\Http\Controllers\InquiryController::class, 'show'])->name('inquiries.show');
    Route::patch('inquiry/{inquiry}/status', [App\Http\Controllers\InquiryController::class, 'updateStatus'])->name('inquiries.update-status');
    Route::delete('inquiry/{inquiry}', [App\Http\Controllers\InquiryController::class, 'destroy'])->name('inquiries.destroy');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
