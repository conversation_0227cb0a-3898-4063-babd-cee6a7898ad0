<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('kost_facilities', function (Blueprint $table) {
            $table->id();
            $table->foreignId('kost_id')->constrained('kosts')->onDelete('cascade');
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('icon', 100)->nullable();
            $table->enum('category', ['kamar', 'kamar_mandi', 'umum', 'keamanan', 'parkir']);
            $table->timestamps();

            $table->index(['kost_id', 'category']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('kost_facilities');
    }
};
