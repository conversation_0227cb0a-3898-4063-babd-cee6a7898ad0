// Test script untuk API endpoints SIM Kost
const BASE_URL = 'http://localhost:8000';

// Helper function untuk HTTP requests
async function makeRequest(url, options = {}) {
    try {
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                ...options.headers
            },
            ...options
        });
        
        const data = await response.json();
        return { status: response.status, data, ok: response.ok };
    } catch (error) {
        console.error('Request failed:', error);
        return { status: 0, data: { error: error.message }, ok: false };
    }
}

// Test data
const testUsers = {
    pencari: {
        name: 'Test Pencari Kost',
        email: '<EMAIL>',
        password: 'password123',
        password_confirmation: 'password123',
        role: 'pencari_kost',
        phone: '081234567890'
    },
    pemilik: {
        name: 'Test Pemilik Kost',
        email: '<EMAIL>',
        password: 'password123',
        password_confirmation: 'password123',
        role: 'pemilik_kost',
        phone: '081234567891'
    }
};

// Test functions
async function testUserRegistration() {
    console.log('\n=== Testing User Registration ===');
    
    // Test registrasi pencari kost
    console.log('\n1. Testing Pencari Kost Registration...');
    const pencariResult = await makeRequest(`${BASE_URL}/api/register`, {
        method: 'POST',
        body: JSON.stringify(testUsers.pencari)
    });
    
    console.log('Pencari Registration Status:', pencariResult.status);
    console.log('Pencari Registration Response:', JSON.stringify(pencariResult.data, null, 2));
    
    // Test registrasi pemilik kost
    console.log('\n2. Testing Pemilik Kost Registration...');
    const pemilikResult = await makeRequest(`${BASE_URL}/api/register`, {
        method: 'POST',
        body: JSON.stringify(testUsers.pemilik)
    });
    
    console.log('Pemilik Registration Status:', pemilikResult.status);
    console.log('Pemilik Registration Response:', JSON.stringify(pemilikResult.data, null, 2));
    
    return { pencari: pencariResult, pemilik: pemilikResult };
}

async function testUserLogin() {
    console.log('\n=== Testing User Login ===');
    
    // Test login pencari kost
    console.log('\n1. Testing Pencari Kost Login...');
    const pencariLogin = await makeRequest(`${BASE_URL}/api/login`, {
        method: 'POST',
        body: JSON.stringify({
            email: testUsers.pencari.email,
            password: testUsers.pencari.password
        })
    });
    
    console.log('Pencari Login Status:', pencariLogin.status);
    console.log('Pencari Login Response:', JSON.stringify(pencariLogin.data, null, 2));
    
    // Test login pemilik kost
    console.log('\n2. Testing Pemilik Kost Login...');
    const pemilikLogin = await makeRequest(`${BASE_URL}/api/login`, {
        method: 'POST',
        body: JSON.stringify({
            email: testUsers.pemilik.email,
            password: testUsers.pemilik.password
        })
    });
    
    console.log('Pemilik Login Status:', pemilikLogin.status);
    console.log('Pemilik Login Response:', JSON.stringify(pemilikLogin.data, null, 2));
    
    return { pencari: pencariLogin, pemilik: pemilikLogin };
}

async function testExistingUserLogin() {
    console.log('\n=== Testing Existing User Login ===');
    
    // Test login dengan admin yang sudah ada
    console.log('\n1. Testing Admin Login...');
    const adminLogin = await makeRequest(`${BASE_URL}/api/login`, {
        method: 'POST',
        body: JSON.stringify({
            email: '<EMAIL>',
            password: 'password'
        })
    });
    
    console.log('Admin Login Status:', adminLogin.status);
    console.log('Admin Login Response:', JSON.stringify(adminLogin.data, null, 2));
    
    return { admin: adminLogin };
}

async function testKostEndpoints() {
    console.log('\n=== Testing Kost Endpoints ===');
    
    // Test get all kosts
    console.log('\n1. Testing Get All Kosts...');
    const allKosts = await makeRequest(`${BASE_URL}/api/kosts`);
    console.log('All Kosts Status:', allKosts.status);
    console.log('All Kosts Count:', allKosts.data?.data?.length || 0);
    
    // Test get popular kosts
    console.log('\n2. Testing Get Popular Kosts...');
    const popularKosts = await makeRequest(`${BASE_URL}/api/kosts/popular`);
    console.log('Popular Kosts Status:', popularKosts.status);
    console.log('Popular Kosts Count:', popularKosts.data?.data?.length || 0);
    
    // Test get cities
    console.log('\n3. Testing Get Cities...');
    const cities = await makeRequest(`${BASE_URL}/api/kosts/cities`);
    console.log('Cities Status:', cities.status);
    console.log('Cities:', cities.data?.data || []);
    
    return { allKosts, popularKosts, cities };
}

// Main test runner
async function runAllTests() {
    console.log('🚀 Starting SIM Kost API Testing...');
    console.log('Base URL:', BASE_URL);
    
    try {
        // Test existing user login first
        const existingLogin = await testExistingUserLogin();
        
        // Test user registration
        const registration = await testUserRegistration();
        
        // Test user login
        const login = await testUserLogin();
        
        // Test kost endpoints
        const kosts = await testKostEndpoints();
        
        console.log('\n=== Test Summary ===');
        console.log('✅ All tests completed!');
        console.log('Check the output above for detailed results.');
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

// Run tests
runAllTests();
