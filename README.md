# 🏠 SIM Kost - Sistem Informasi Manajemen Kost

Sistem Informasi Manajemen Kost yang modern dengan fitur AI-powered search, built dengan Laravel 11, React, TypeScript, dan Inertia.js.

## ✨ Fitur Utama

### 🤖 AI-Powered Search
- Pencarian kost menggunakan natural language processing
- Integrasi dengan Groq AI untuk interpretasi query
- Rekomendasi cerdas berdasarkan preferensi user

### 👥 Multi-Role System
- **Pencari Kost**: Cari dan inquiry kost
- **Pemilik Kost**: Ke<PERSON>la kost dan respon inquiry
- **Admin**: Moderasi konten dan monitoring sistem

### 🔔 Real-time Notifications
- Notifikasi real-time menggunakan Laravel Broadcasting
- Push notifications untuk inquiry baru
- Status update notifications

### 📱 Modern UI/UX
- Responsive design dengan Tailwind CSS
- Dark mode support
- Interactive components dengan shadcn/ui
- TypeScript untuk type safety

### 🔐 Security & Authentication
- Laravel Sanctum untuk API authentication
- Role-based access control
- Email verification
- Password reset functionality

## 🛠️ Tech Stack

### Backend
- **Laravel 11** - PHP Framework
- **MySQL** - Database
- **Laravel Sanctum** - API Authentication
- **Laravel Broadcasting** - Real-time features
- **Intervention Image** - Image processing

### Frontend
- **React 18** - UI Library
- **TypeScript** - Type safety
- **Inertia.js** - SPA without API
- **Tailwind CSS** - Styling
- **shadcn/ui** - UI Components
- **Lucide React** - Icons

### AI Integration
- **Groq AI** - Natural language processing
- **Custom AI Service** - Search interpretation

## 🚀 Installation

### Prerequisites
- PHP 8.2+
- Node.js 18+
- MySQL 8.0+
- Composer
- npm/yarn

### 1. Clone Repository
```bash
git clone https://github.com/your-username/sim-kost.git
cd sim-kost
```

### 2. Install Dependencies
```bash
# Backend dependencies
composer install

# Frontend dependencies
npm install
```

### 3. Environment Setup
```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

### 4. Database Setup
```bash
# Configure database in .env file
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=sim_kost
DB_USERNAME=your_username
DB_PASSWORD=your_password

# Run migrations and seeders
php artisan migrate --seed
```

### 5. Storage Setup
```bash
# Create storage link
php artisan storage:link

# Set permissions (Linux/Mac)
chmod -R 775 storage bootstrap/cache
```

### 6. AI Configuration
```bash
# Add Groq AI configuration to .env
GROQ_API_KEY=your_groq_api_key_here
GROQ_MODEL=llama3-8b-8192
GROQ_MAX_TOKENS=8192
GROQ_TEMPERATURE=1
```

### 7. Build Assets
```bash
# Development
npm run dev

# Production
npm run build
```

### 8. Start Development Server
```bash
# Backend server
php artisan serve

# Frontend development server (separate terminal)
npm run dev
```

## 🔧 Configuration

### Environment Variables
```env
# Application
APP_NAME="SIM Kost"
APP_ENV=local
APP_KEY=base64:...
APP_DEBUG=true
APP_URL=http://localhost

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=sim_kost
DB_USERNAME=root
DB_PASSWORD=

# AI Configuration
GROQ_API_KEY=your_groq_api_key_here
GROQ_MODEL=llama3-8b-8192
GROQ_MAX_TOKENS=8192
GROQ_TEMPERATURE=1

# Broadcasting (for real-time features)
BROADCAST_DRIVER=pusher
PUSHER_APP_ID=your_pusher_app_id
PUSHER_APP_KEY=your_pusher_key
PUSHER_APP_SECRET=your_pusher_secret
PUSHER_APP_CLUSTER=your_cluster

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
```

## 📖 API Documentation

### Authentication Endpoints
```
POST /api/register - User registration
POST /api/login - User login
POST /api/logout - User logout
GET /api/user - Get authenticated user
```

### Kost Endpoints
```
GET /api/kosts - List all kosts (with filters)
GET /api/kosts/{id} - Get single kost
GET /api/kosts/popular - Get popular kosts
GET /api/kosts/latest - Get latest kosts
GET /api/kosts/cities - Get available cities
GET /api/my-kosts - Get user's kosts (owner only)
```

### AI Search Endpoints
```
POST /api/ai-search/search - AI-powered search
GET /api/ai-search/history - Get search history
GET /api/ai-search/stats - Get search statistics
```

### Inquiry Endpoints
```
GET /api/inquiries - List inquiries
POST /api/inquiries - Create inquiry
GET /api/inquiries/{id} - Get inquiry details
PATCH /api/inquiries/{id}/status - Update inquiry status
DELETE /api/inquiries/{id} - Delete inquiry
```

## 🧪 Testing

### Run Tests
```bash
# Run all tests
php artisan test

# Run specific test suite
php artisan test --testsuite=Feature
php artisan test --testsuite=Unit

# Run with coverage
php artisan test --coverage
```

### Generate Test Data
```bash
# Generate test data for development
php artisan kost:generate-test-data

# Generate fresh test data (clears existing)
php artisan kost:generate-test-data --fresh
```

## 🔧 Maintenance Commands

### Cleanup Commands
```bash
# Cleanup unused images
php artisan kost:cleanup-images

# Send reminder notifications
php artisan kost:send-reminders

# Cleanup old notifications
php artisan kost:cleanup-notifications
```

### Scheduled Tasks
Add to your cron:
```bash
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

## 🚀 Deployment

### Production Setup
1. Set `APP_ENV=production` in `.env`
2. Set `APP_DEBUG=false`
3. Configure production database
4. Set up proper web server (Nginx/Apache)
5. Configure SSL certificate
6. Set up queue workers
7. Configure broadcasting for real-time features

### Queue Workers
```bash
# Start queue worker
php artisan queue:work

# Supervisor configuration recommended for production
```

### Optimization
```bash
# Cache configuration
php artisan config:cache

# Cache routes
php artisan route:cache

# Cache views
php artisan view:cache

# Optimize autoloader
composer install --optimize-autoloader --no-dev
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👥 Team

- **Developer**: Your Name
- **UI/UX Designer**: Designer Name
- **Project Manager**: PM Name

## 📞 Support

For support, email <EMAIL> or join our Slack channel.

## 🙏 Acknowledgments

- Laravel community
- React community
- Groq AI for natural language processing
- shadcn for amazing UI components
- All contributors and testers

---

Made with ❤️ by SIM Kost Team
