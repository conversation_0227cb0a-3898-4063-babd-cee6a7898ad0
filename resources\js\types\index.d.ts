import { LucideIcon } from 'lucide-react';
import type { Config } from 'ziggy-js';

export interface Auth {
    user: User;
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavGroup {
    title: string;
    items: NavItem[];
}

export interface NavItem {
    title: string;
    href: string;
    icon?: LucideIcon | null;
    isActive?: boolean;
}

export interface SharedData {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    ziggy: Config & { location: string };
    sidebarOpen: boolean;
    [key: string]: unknown;
}

export interface User {
    id: number;
    name: string;
    email: string;
    avatar?: string;
    email_verified_at: string | null;
    role: 'pencari_kost' | 'pemilik_kost' | 'admin';
    phone?: string;
    is_active: boolean;
    created_at: string;
    updated_at: string;
    [key: string]: unknown; // This allows for additional properties...
}

export interface KostFacility {
    id: number;
    kost_id: number;
    name: string;
    description?: string;
    icon?: string;
    category: 'kamar' | 'kamar_mandi' | 'umum' | 'keamanan' | 'parkir';
    category_name: string;
}

export interface KostImage {
    id: number;
    kost_id: number;
    image_path: string;
    image_type: 'cover' | 'room' | 'facility' | 'exterior';
    alt_text?: string;
    sort_order: number;
    image_url: string;
    image_type_name: string;
}

export interface Kost {
    id: number;
    owner_id: number;
    name: string;
    description: string;
    address: string;
    city: string;
    province: string;
    postal_code?: string;
    latitude?: number;
    longitude?: number;
    price_monthly: number;
    price_daily?: number;
    room_count: number;
    available_rooms: number;
    gender_type: 'putra' | 'putri' | 'campur';
    kost_type: 'bulanan' | 'harian' | 'keduanya';
    status: 'draft' | 'pending' | 'approved' | 'rejected';
    is_active: boolean;
    formatted_price: string;
    created_at: string;
    updated_at: string;
    owner?: User;
    facilities?: KostFacility[];
    images?: KostImage[];
    inquiries?: Inquiry[];
}

export interface Inquiry {
    id: number;
    kost_id: number;
    user_id: number;
    message: string;
    contact_preference: 'email' | 'phone' | 'whatsapp';
    status: 'pending' | 'responded' | 'closed';
    responded_at?: string;
    created_at: string;
    updated_at: string;
    kost?: Kost;
    user?: User;
    status_name: string;
    contact_preference_name: string;
}

export interface Notification {
    id: number;
    user_id: number;
    title: string;
    message: string;
    type: 'inquiry' | 'kost_approved' | 'kost_rejected' | 'system';
    data?: any;
    read_at?: string;
    created_at: string;
    updated_at: string;
    type_name: string;
}

export interface AiSearchLog {
    id: number;
    user_id?: number;
    query: string;
    ai_response?: string;
    results_count: number;
    response_time?: number;
    formatted_response_time: string;
    created_at: string;
    updated_at: string;
}

export interface AiSearchResult {
    success: boolean;
    message: string;
    data?: {
        query: string;
        results: Kost[];
        ai_interpretation?: string;
        search_criteria?: any;
        recommendations?: string[];
        response_time: number;
        total_results: number;
    };
    error?: string;
}

export type PageProps<
    T extends Record<string, unknown> = Record<string, unknown>,
> = T & {
    auth: {
        user: User;
    };
};
