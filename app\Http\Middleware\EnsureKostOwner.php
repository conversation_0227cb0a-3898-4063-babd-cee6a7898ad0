<?php

namespace App\Http\Middleware;

use App\Models\Kost;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class EnsureKostOwner
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();
        $kost = $request->route('kost');

        // If kost parameter exists, check ownership
        if ($kost instanceof Kost) {
            if ($user->id !== $kost->owner_id && !$user->isAdmin()) {
                abort(403, 'Unauthorized access to this kost.');
            }
        }

        return $next($request);
    }
}
