import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import AppLayout from '@/layouts/app-layout';
import { AiSearchLog, Kost, PageProps, User } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { 
    Users, 
    Home, 
    MessageCircle, 
    Search, 
    CheckCircle, 
    Clock, 
    AlertTriangle,
    TrendingUp,
    Eye,
    UserCheck,
    Building,
    Activity,
    Sparkles,
    Calendar
} from 'lucide-react';
import React from 'react';

interface AdminDashboardProps extends PageProps {
    stats: {
        total_users: number;
        total_pencari: number;
        total_pemilik: number;
        total_kosts: number;
        pending_kosts: number;
        approved_kosts: number;
        total_inquiries: number;
        total_ai_searches: number;
    };
    monthlyStats: {
        users_this_month: number;
        kosts_this_month: number;
        inquiries_this_month: number;
        ai_searches_this_month: number;
    };
    pendingKosts: Kost[];
    recentUsers: User[];
    recentAiSearches: AiSearchLog[];
}

export default function AdminDashboard({
    auth,
    stats,
    monthlyStats,
    pendingKosts,
    recentUsers,
    recentAiSearches,
}: AdminDashboardProps) {
    const handleApproveKost = (kostId: number) => {
        router.post(route('admin.kosts.approve', kostId));
    };

    const handleViewKost = (kostId: number) => {
        router.visit(route('admin.kosts.detail', kostId));
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'approved': return 'bg-green-100 text-green-800';
            case 'pending': return 'bg-yellow-100 text-yellow-800';
            case 'rejected': return 'bg-red-100 text-red-800';
            case 'draft': return 'bg-gray-100 text-gray-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getRoleColor = (role: string) => {
        switch (role) {
            case 'admin': return 'bg-purple-100 text-purple-800';
            case 'pemilik_kost': return 'bg-blue-100 text-blue-800';
            case 'pencari_kost': return 'bg-green-100 text-green-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getRoleName = (role: string) => {
        switch (role) {
            case 'admin': return 'Admin';
            case 'pemilik_kost': return 'Pemilik Kost';
            case 'pencari_kost': return 'Pencari Kost';
            default: return role;
        }
    };

    return (
        <AppLayout>
            <Head title="Dashboard Admin" />

            <div className="space-y-8">
                {/* Welcome Section */}
                <div className="bg-gradient-to-r from-purple-500/10 to-purple-500/5 rounded-lg p-6">
                    <h1 className="text-2xl font-bold mb-2">
                        Dashboard Admin 👑
                    </h1>
                    <p className="text-muted-foreground">
                        Kelola sistem SIM Kost, verifikasi konten, dan monitor aktivitas pengguna.
                    </p>
                </div>

                {/* Main Stats */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total User</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_users}</div>
                            <p className="text-xs text-muted-foreground">
                                +{monthlyStats.users_this_month} bulan ini
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Kost</CardTitle>
                            <Home className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_kosts}</div>
                            <p className="text-xs text-muted-foreground">
                                +{monthlyStats.kosts_this_month} bulan ini
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Perlu Review</CardTitle>
                            <Clock className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.pending_kosts}</div>
                            <p className="text-xs text-muted-foreground">
                                Kost menunggu persetujuan
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">AI Searches</CardTitle>
                            <Sparkles className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_ai_searches}</div>
                            <p className="text-xs text-muted-foreground">
                                +{monthlyStats.ai_searches_this_month} bulan ini
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Secondary Stats */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Pencari Kost</CardTitle>
                            <UserCheck className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_pencari}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Pemilik Kost</CardTitle>
                            <Building className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_pemilik}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Kost Disetujui</CardTitle>
                            <CheckCircle className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.approved_kosts}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Inquiry</CardTitle>
                            <MessageCircle className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_inquiries}</div>
                        </CardContent>
                    </Card>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Pending Kosts */}
                    <div className="lg:col-span-2 space-y-6">
                        <div className="flex items-center justify-between">
                            <h2 className="text-xl font-semibold flex items-center gap-2">
                                <Clock className="h-5 w-5" />
                                Kost Perlu Review
                            </h2>
                            <Button variant="outline" asChild>
                                <Link href={route('admin.kosts', { status: 'pending' })}>
                                    <Eye className="h-4 w-4 mr-2" />
                                    Lihat Semua
                                </Link>
                            </Button>
                        </div>

                        <div className="space-y-4">
                            {pendingKosts.length > 0 ? (
                                pendingKosts.map((kost) => (
                                    <Card key={kost.id}>
                                        <CardContent className="p-4">
                                            <div className="flex items-start justify-between">
                                                <div className="flex gap-4">
                                                    <div className="w-16 h-16 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
                                                        {kost.images && kost.images.length > 0 ? (
                                                            <img
                                                                src={kost.images[0].image_url}
                                                                alt={kost.name}
                                                                className="w-full h-full object-cover"
                                                            />
                                                        ) : (
                                                            <div className="w-full h-full flex items-center justify-center">
                                                                <Home className="h-6 w-6 text-gray-400" />
                                                            </div>
                                                        )}
                                                    </div>
                                                    <div className="flex-1 min-w-0">
                                                        <h3 className="font-medium truncate">{kost.name}</h3>
                                                        <p className="text-sm text-muted-foreground">
                                                            {kost.city}, {kost.province}
                                                        </p>
                                                        <p className="text-sm text-muted-foreground">
                                                            Pemilik: {kost.owner?.name}
                                                        </p>
                                                        <p className="text-sm font-medium text-primary">
                                                            {kost.formatted_price}/bulan
                                                        </p>
                                                    </div>
                                                </div>
                                                <div className="flex gap-2">
                                                    <Button
                                                        size="sm"
                                                        variant="outline"
                                                        onClick={() => handleViewKost(kost.id)}
                                                    >
                                                        <Eye className="h-3 w-3 mr-1" />
                                                        Detail
                                                    </Button>
                                                    <Button
                                                        size="sm"
                                                        onClick={() => handleApproveKost(kost.id)}
                                                    >
                                                        <CheckCircle className="h-3 w-3 mr-1" />
                                                        Setujui
                                                    </Button>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                ))
                            ) : (
                                <Card>
                                    <CardContent className="flex items-center justify-center py-8">
                                        <div className="text-center text-muted-foreground">
                                            <CheckCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
                                            <p>Tidak ada kost yang perlu direview</p>
                                        </div>
                                    </CardContent>
                                </Card>
                            )}
                        </div>
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        {/* Recent Users */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Users className="h-5 w-5" />
                                    User Terbaru
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {recentUsers.length > 0 ? (
                                    <>
                                        {recentUsers.map((user) => (
                                            <div key={user.id} className="flex items-center justify-between">
                                                <div className="flex items-center gap-3">
                                                    <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                                                        <span className="text-xs font-medium text-primary">
                                                            {user.name.charAt(0).toUpperCase()}
                                                        </span>
                                                    </div>
                                                    <div className="min-w-0 flex-1">
                                                        <p className="font-medium text-sm truncate">{user.name}</p>
                                                        <p className="text-xs text-muted-foreground truncate">{user.email}</p>
                                                    </div>
                                                </div>
                                                <Badge className={getRoleColor(user.role)}>
                                                    {getRoleName(user.role)}
                                                </Badge>
                                            </div>
                                        ))}
                                        <Separator />
                                        <Button variant="outline" size="sm" className="w-full" asChild>
                                            <Link href={route('admin.users')}>
                                                Kelola User
                                            </Link>
                                        </Button>
                                    </>
                                ) : (
                                    <div className="text-center text-muted-foreground py-4">
                                        <Users className="h-6 w-6 mx-auto mb-2 opacity-50" />
                                        <p className="text-sm">Belum ada user baru</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Recent AI Searches */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Sparkles className="h-5 w-5" />
                                    AI Search Terbaru
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {recentAiSearches.length > 0 ? (
                                    <>
                                        {recentAiSearches.map((search) => (
                                            <div key={search.id} className="space-y-1">
                                                <div className="flex items-start justify-between">
                                                    <p className="font-medium text-sm">
                                                        {search.user?.name || 'Guest'}
                                                    </p>
                                                    <span className="text-xs text-muted-foreground">
                                                        {search.formatted_response_time}
                                                    </span>
                                                </div>
                                                <p className="text-sm text-muted-foreground line-clamp-2">
                                                    {search.query}
                                                </p>
                                                <p className="text-xs text-muted-foreground">
                                                    {search.results_count} hasil • {new Date(search.created_at).toLocaleDateString('id-ID')}
                                                </p>
                                            </div>
                                        ))}
                                        <Separator />
                                        <Button variant="outline" size="sm" className="w-full" asChild>
                                            <Link href={route('admin.ai-search-logs')}>
                                                Lihat Log AI Search
                                            </Link>
                                        </Button>
                                    </>
                                ) : (
                                    <div className="text-center text-muted-foreground py-4">
                                        <Sparkles className="h-6 w-6 mx-auto mb-2 opacity-50" />
                                        <p className="text-sm">Belum ada pencarian AI</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Quick Actions */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Activity className="h-5 w-5" />
                                    Aksi Cepat
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-2">
                                <Button variant="outline" size="sm" className="w-full justify-start" asChild>
                                    <Link href={route('admin.reports')}>
                                        <TrendingUp className="h-4 w-4 mr-2" />
                                        Lihat Laporan
                                    </Link>
                                </Button>
                                <Button variant="outline" size="sm" className="w-full justify-start" asChild>
                                    <Link href={route('admin.users')}>
                                        <Users className="h-4 w-4 mr-2" />
                                        Kelola User
                                    </Link>
                                </Button>
                                <Button variant="outline" size="sm" className="w-full justify-start" asChild>
                                    <Link href={route('admin.kosts')}>
                                        <Home className="h-4 w-4 mr-2" />
                                        Kelola Kost
                                    </Link>
                                </Button>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
