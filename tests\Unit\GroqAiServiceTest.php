<?php

namespace Tests\Unit;

use App\Services\GroqAiService;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class GroqAiServiceTest extends TestCase
{
    private GroqAiService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new GroqAiService();
    }

    public function test_service_can_be_instantiated(): void
    {
        $this->assertInstanceOf(GroqAiService::class, $this->service);
    }

    public function test_is_available_returns_boolean(): void
    {
        $result = $this->service->isAvailable();
        $this->assertIsBool($result);
    }

    public function test_service_has_required_properties(): void
    {
        $reflection = new \ReflectionClass($this->service);

        $this->assertTrue($reflection->hasProperty('client'));
        $this->assertTrue($reflection->hasProperty('apiKey'));
        $this->assertTrue($reflection->hasProperty('apiUrl'));
        $this->assertTrue($reflection->hasProperty('model'));
        $this->assertTrue($reflection->hasProperty('maxTokens'));
        $this->assertTrue($reflection->hasProperty('temperature'));
    }

    public function test_service_configuration_is_loaded(): void
    {
        $reflection = new \ReflectionClass($this->service);

        $apiKeyProperty = $reflection->getProperty('apiKey');
        $apiKeyProperty->setAccessible(true);
        $apiKey = $apiKeyProperty->getValue($this->service);

        $this->assertEquals('test-api-key', $apiKey);

        $modelProperty = $reflection->getProperty('model');
        $modelProperty->setAccessible(true);
        $model = $modelProperty->getValue($this->service);

        $this->assertEquals('llama3-8b-8192', $model);
    }
}
