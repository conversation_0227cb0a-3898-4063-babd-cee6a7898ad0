<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class KostImage extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'kost_id',
        'image_path',
        'image_type',
        'alt_text',
        'sort_order',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected function casts(): array
    {
        return [
            'sort_order' => 'integer',
        ];
    }

    /**
     * Konstanta untuk tipe gambar
     */
    public const TYPE_COVER = 'cover';
    public const TYPE_ROOM = 'room';
    public const TYPE_FACILITY = 'facility';
    public const TYPE_EXTERIOR = 'exterior';

    /**
     * Mendapatkan semua tipe gambar yang tersedia
     */
    public static function getImageTypes(): array
    {
        return [
            self::TYPE_COVER => 'Gambar Cover',
            self::TYPE_ROOM => 'Gambar Kamar',
            self::TYPE_FACILITY => 'Gambar Fasilitas',
            self::TYPE_EXTERIOR => 'Gambar Eksterior',
        ];
    }

    /**
     * Relationship: Gambar milik satu kost
     */
    public function kost(): BelongsTo
    {
        return $this->belongsTo(Kost::class);
    }

    /**
     * Scope: Filter berdasarkan tipe gambar
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('image_type', $type);
    }

    /**
     * Scope: Urutkan berdasarkan sort_order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Mendapatkan URL gambar lengkap
     */
    public function getImageUrlAttribute(): string
    {
        return Storage::url($this->image_path);
    }

    /**
     * Mendapatkan nama tipe gambar yang diformat
     */
    public function getImageTypeNameAttribute(): string
    {
        $types = self::getImageTypes();
        return $types[$this->image_type] ?? $this->image_type;
    }
}
