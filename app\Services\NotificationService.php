<?php

namespace App\Services;

use App\Events\KostStatusUpdated;
use App\Events\NewInquiryNotification;
use App\Models\Inquiry;
use App\Models\Kost;
use App\Models\Notification;
use App\Models\User;

class NotificationService
{
    /**
     * Kirim notifikasi inquiry baru ke pemilik kost
     */
    public function sendNewInquiryNotification(Inquiry $inquiry): void
    {
        $inquiry->load(['kost', 'user']);
        
        // Buat notifikasi di database
        $notification = Notification::createInquiryNotification(
            $inquiry->kost->owner_id,
            $inquiry->kost->name,
            $inquiry->id
        );

        // Broadcast real-time notification
        broadcast(new NewInquiryNotification($inquiry, $notification));
    }

    /**
     * Kirim notifikasi perubahan status kost
     */
    public function sendKostStatusNotification(Kost $kost, string $oldStatus): void
    {
        $notification = null;

        if ($kost->status === 'approved') {
            $notification = Notification::createKostApprovedNotification(
                $kost->owner_id,
                $kost->name
            );
        } elseif ($kost->status === 'rejected') {
            $notification = Notification::createKostRejectedNotification(
                $kost->owner_id,
                $kost->name
            );
        }

        if ($notification) {
            // Broadcast real-time notification
            broadcast(new KostStatusUpdated($kost, $notification, $oldStatus));
        }
    }

    /**
     * Kirim notifikasi sistem ke user tertentu
     */
    public function sendSystemNotification(int $userId, string $title, string $message, array $data = []): Notification
    {
        return Notification::create([
            'user_id' => $userId,
            'title' => $title,
            'message' => $message,
            'type' => Notification::TYPE_SYSTEM,
            'data' => $data,
        ]);
    }

    /**
     * Kirim notifikasi broadcast ke semua user dengan role tertentu
     */
    public function sendBroadcastNotification(string $role, string $title, string $message, array $data = []): int
    {
        $users = User::byRole($role)->active()->get();
        $count = 0;

        foreach ($users as $user) {
            $this->sendSystemNotification($user->id, $title, $message, $data);
            $count++;
        }

        return $count;
    }

    /**
     * Tandai notifikasi sebagai sudah dibaca
     */
    public function markAsRead(int $notificationId, int $userId): bool
    {
        $notification = Notification::where('id', $notificationId)
            ->where('user_id', $userId)
            ->first();

        if ($notification) {
            $notification->markAsRead();
            return true;
        }

        return false;
    }

    /**
     * Tandai semua notifikasi user sebagai sudah dibaca
     */
    public function markAllAsRead(int $userId): int
    {
        return Notification::where('user_id', $userId)
            ->whereNull('read_at')
            ->update(['read_at' => now()]);
    }

    /**
     * Hapus notifikasi lama (lebih dari 30 hari)
     */
    public function cleanupOldNotifications(): int
    {
        return Notification::where('created_at', '<', now()->subDays(30))->delete();
    }

    /**
     * Dapatkan statistik notifikasi untuk user
     */
    public function getUserNotificationStats(int $userId): array
    {
        $user = User::find($userId);
        
        if (!$user) {
            return [];
        }

        return [
            'total_notifications' => $user->notifications()->count(),
            'unread_notifications' => $user->notifications()->unread()->count(),
            'read_notifications' => $user->notifications()->read()->count(),
            'notifications_by_type' => $user->notifications()
                ->selectRaw('type, COUNT(*) as count')
                ->groupBy('type')
                ->pluck('count', 'type')
                ->toArray(),
            'recent_notifications' => $user->notifications()
                ->latest()
                ->limit(5)
                ->get()
                ->map(function ($notification) {
                    return [
                        'id' => $notification->id,
                        'title' => $notification->title,
                        'message' => $notification->message,
                        'type' => $notification->type,
                        'type_name' => $notification->type_name,
                        'read_at' => $notification->read_at,
                        'created_at' => $notification->created_at,
                    ];
                }),
        ];
    }

    /**
     * Dapatkan notifikasi untuk user dengan pagination
     */
    public function getUserNotifications(int $userId, int $perPage = 15, bool $unreadOnly = false): array
    {
        $query = Notification::where('user_id', $userId);

        if ($unreadOnly) {
            $query->unread();
        }

        $notifications = $query->latest()->paginate($perPage);

        return [
            'data' => $notifications->items(),
            'meta' => [
                'current_page' => $notifications->currentPage(),
                'last_page' => $notifications->lastPage(),
                'per_page' => $notifications->perPage(),
                'total' => $notifications->total(),
            ],
        ];
    }

    /**
     * Kirim notifikasi welcome untuk user baru
     */
    public function sendWelcomeNotification(User $user): void
    {
        $message = match ($user->role) {
            User::ROLE_PENCARI_KOST => 'Selamat datang di SIM Kost! Mulai cari kost impian Anda dengan fitur pencarian AI kami.',
            User::ROLE_PEMILIK_KOST => 'Selamat datang di SIM Kost! Daftarkan kost Anda dan dapatkan penyewa dengan mudah.',
            User::ROLE_ADMIN => 'Selamat datang Admin! Anda memiliki akses penuh untuk mengelola sistem SIM Kost.',
            default => 'Selamat datang di SIM Kost!',
        };

        $this->sendSystemNotification(
            $user->id,
            'Selamat Datang!',
            $message,
            ['user_role' => $user->role]
        );
    }

    /**
     * Kirim reminder untuk kost yang belum disubmit
     */
    public function sendKostSubmissionReminder(): int
    {
        $draftKosts = Kost::where('status', 'draft')
            ->where('created_at', '<', now()->subDays(3))
            ->with('owner')
            ->get();

        $count = 0;

        foreach ($draftKosts as $kost) {
            $this->sendSystemNotification(
                $kost->owner_id,
                'Reminder: Submit Kost Anda',
                "Kost '{$kost->name}' masih dalam status draft. Silakan lengkapi data dan submit untuk review admin.",
                ['kost_id' => $kost->id]
            );
            $count++;
        }

        return $count;
    }

    /**
     * Kirim notifikasi inquiry yang belum direspon
     */
    public function sendUnrespondedInquiryReminder(): int
    {
        $pendingInquiries = Inquiry::with(['kost.owner', 'user'])
            ->pending()
            ->where('created_at', '<', now()->subDays(2))
            ->get();

        $count = 0;

        foreach ($pendingInquiries as $inquiry) {
            $this->sendSystemNotification(
                $inquiry->kost->owner_id,
                'Reminder: Inquiry Belum Direspon',
                "Anda memiliki inquiry dari {$inquiry->user->name} untuk kost '{$inquiry->kost->name}' yang belum direspon.",
                ['inquiry_id' => $inquiry->id]
            );
            $count++;
        }

        return $count;
    }
}
