// Test script untuk Inquiry & Communication Flow
const BASE_URL = 'http://localhost:8000';

// Helper function untuk HTTP requests
async function makeRequest(url, options = {}) {
    try {
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                ...options.headers
            },
            ...options
        });
        
        const data = await response.json();
        return { status: response.status, data, ok: response.ok };
    } catch (error) {
        console.error('Request failed:', error);
        return { status: 0, data: { error: error.message }, ok: false };
    }
}

let pencariToken = '';
let pemilikToken = '';
let adminToken = '';

async function loginUsers() {
    console.log('\n=== Login All Users ===');
    
    // Login pencari
    const pencariLogin = await makeRequest(`${BASE_URL}/api/login`, {
        method: 'POST',
        body: JSON.stringify({
            email: '<EMAIL>',
            password: 'password123'
        })
    });
    
    if (pencariLogin.ok) {
        pencariToken = pencariLogin.data.data.token;
        console.log('✅ Pencari login successful');
    }
    
    // Login pemilik
    const pemilikLogin = await makeRequest(`${BASE_URL}/api/login`, {
        method: 'POST',
        body: JSON.stringify({
            email: '<EMAIL>',
            password: 'password123'
        })
    });
    
    if (pemilikLogin.ok) {
        pemilikToken = pemilikLogin.data.data.token;
        console.log('✅ Pemilik login successful');
    }
    
    // Login admin
    const adminLogin = await makeRequest(`${BASE_URL}/api/login`, {
        method: 'POST',
        body: JSON.stringify({
            email: '<EMAIL>',
            password: 'password'
        })
    });
    
    if (adminLogin.ok) {
        adminToken = adminLogin.data.data.token;
        console.log('✅ Admin login successful');
    }
    
    return { pencariToken, pemilikToken, adminToken };
}

async function testInquiryCreation() {
    console.log('\n=== Testing Inquiry Creation ===');
    
    // Get available kosts
    const kostsResult = await makeRequest(`${BASE_URL}/api/kosts`);
    
    if (kostsResult.data?.data?.length > 0) {
        const kost = kostsResult.data.data[0];
        console.log(`Testing inquiry for kost: ${kost.name} (ID: ${kost.id})`);
        
        // Create inquiry via API
        const inquiryData = {
            kost_id: kost.id,
            message: 'Halo, saya tertarik dengan kost ini. Apakah masih ada kamar yang tersedia? Mohon info lebih lanjut mengenai fasilitas dan aturan kost.',
            contact_preference: 'whatsapp'
        };
        
        console.log('Creating inquiry with data:', inquiryData);
        
        // Try different API endpoints for inquiry creation
        const endpoints = [
            `/api/inquiries`,
            `/api/kosts/${kost.id}/inquiries`
        ];
        
        for (const endpoint of endpoints) {
            console.log(`\n--- Trying endpoint: ${endpoint} ---`);
            
            const inquiryResult = await makeRequest(`${BASE_URL}${endpoint}`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${pencariToken}`
                },
                body: JSON.stringify(inquiryData)
            });
            
            console.log('Inquiry Creation Status:', inquiryResult.status);
            console.log('Inquiry Creation Response:', JSON.stringify(inquiryResult.data, null, 2));
            
            if (inquiryResult.ok) {
                return inquiryResult.data;
            }
        }
    } else {
        console.log('❌ No kosts available for inquiry testing');
    }
    
    return null;
}

async function testInquiryListing() {
    console.log('\n=== Testing Inquiry Listing ===');
    
    // Test as pencari (get my inquiries)
    console.log('\n1. Testing as Pencari (My Inquiries)...');
    const pencariInquiries = await makeRequest(`${BASE_URL}/api/inquiries`, {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${pencariToken}`
        }
    });
    
    console.log('Pencari Inquiries Status:', pencariInquiries.status);
    console.log('Pencari Inquiries Count:', pencariInquiries.data?.data?.length || 0);
    
    // Test as pemilik (get inquiries for my kosts)
    console.log('\n2. Testing as Pemilik (Inquiries for My Kosts)...');
    const pemilikInquiries = await makeRequest(`${BASE_URL}/api/inquiries`, {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${pemilikToken}`
        }
    });
    
    console.log('Pemilik Inquiries Status:', pemilikInquiries.status);
    console.log('Pemilik Inquiries Count:', pemilikInquiries.data?.data?.length || 0);
    
    return { pencariInquiries, pemilikInquiries };
}

async function testInquiryResponse() {
    console.log('\n=== Testing Inquiry Response ===');
    
    // Get inquiries as pemilik
    const inquiriesResult = await makeRequest(`${BASE_URL}/api/inquiries`, {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${pemilikToken}`
        }
    });
    
    if (inquiriesResult.data?.data?.length > 0) {
        const inquiry = inquiriesResult.data.data[0];
        console.log(`Responding to inquiry ID: ${inquiry.id}`);
        
        const responseData = {
            status: 'responded',
            response_message: 'Terima kasih atas minat Anda. Kamar masih tersedia. Silakan hubungi saya di WhatsApp untuk informasi lebih lanjut dan jadwal kunjungan.'
        };
        
        const responseResult = await makeRequest(`${BASE_URL}/api/inquiries/${inquiry.id}/status`, {
            method: 'PATCH',
            headers: {
                'Authorization': `Bearer ${pemilikToken}`
            },
            body: JSON.stringify(responseData)
        });
        
        console.log('Inquiry Response Status:', responseResult.status);
        console.log('Inquiry Response Result:', JSON.stringify(responseResult.data, null, 2));
        
        return responseResult;
    } else {
        console.log('❌ No inquiries available for response testing');
        return null;
    }
}

async function testNotificationSystem() {
    console.log('\n=== Testing Notification System ===');
    
    // This would typically test:
    console.log('🔔 Notification system testing would include:');
    console.log('1. Real-time notifications when inquiry is created');
    console.log('2. Email notifications to kost owner');
    console.log('3. Push notifications (if implemented)');
    console.log('4. Notification status updates');
    console.log('5. Notification history');
    
    // Test notification endpoints if available
    const notificationEndpoints = [
        '/api/notifications',
        '/api/user/notifications'
    ];
    
    for (const endpoint of notificationEndpoints) {
        console.log(`\n--- Testing endpoint: ${endpoint} ---`);
        
        const notifResult = await makeRequest(`${BASE_URL}${endpoint}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${pemilikToken}`
            }
        });
        
        console.log('Notification Status:', notifResult.status);
        if (notifResult.ok) {
            console.log('Notifications Count:', notifResult.data?.data?.length || 0);
        } else {
            console.log('Notification Error:', notifResult.data);
        }
    }
}

async function testWebInterfaceWorkflow() {
    console.log('\n=== Testing Web Interface Workflow ===');
    
    console.log('🌐 Complete web interface testing would include:');
    console.log('\n📱 Pencari Kost Workflow:');
    console.log('1. Browse kosts on homepage');
    console.log('2. Use search filters');
    console.log('3. View kost details');
    console.log('4. Submit inquiry form');
    console.log('5. View inquiry status');
    console.log('6. Receive notifications');
    
    console.log('\n🏠 Pemilik Kost Workflow:');
    console.log('1. Receive inquiry notifications');
    console.log('2. View inquiry details');
    console.log('3. Respond to inquiries');
    console.log('4. Update inquiry status');
    console.log('5. Manage kost listings');
    
    console.log('\n👨‍💼 Admin Workflow:');
    console.log('1. Monitor all inquiries');
    console.log('2. Moderate content');
    console.log('3. Manage user accounts');
    console.log('4. View system statistics');
}

// Main test runner
async function runInquiryFlowTests() {
    console.log('💬 Starting Inquiry & Communication Flow Testing...');
    console.log('Base URL:', BASE_URL);
    
    try {
        // Login all users
        await loginUsers();
        
        // Test inquiry creation
        await testInquiryCreation();
        
        // Test inquiry listing
        await testInquiryListing();
        
        // Test inquiry response
        await testInquiryResponse();
        
        // Test notification system
        await testNotificationSystem();
        
        // Test web interface workflow
        await testWebInterfaceWorkflow();
        
        console.log('\n=== Inquiry Flow Test Summary ===');
        console.log('✅ API tests completed!');
        console.log('📝 Note: Full workflow testing requires web interface automation');
        console.log('🔔 Real-time notifications require WebSocket testing');
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

// Run tests
runInquiryFlowTests();
