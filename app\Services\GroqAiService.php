<?php

namespace App\Services;

use App\Models\AiSearchLog;
use App\Models\Kost;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Log;

class GroqAiService
{
    private Client $client;
    private string $apiKey;
    private string $apiUrl;
    private string $model;
    private int $maxTokens;
    private float $temperature;

    public function __construct()
    {
        $this->client = new Client();
        $this->apiKey = config('services.groq.api_key');
        $this->apiUrl = config('services.groq.api_url');
        $this->model = config('services.groq.model');
        $this->maxTokens = config('services.groq.max_tokens');
        $this->temperature = config('services.groq.temperature');
    }

    /**
     * Mencari kost berdasarkan query natural language menggunakan AI
     */
    public function searchKosts(string $query, ?int $userId = null): array
    {
        $startTime = microtime(true);
        
        try {
            // Dapatkan konteks kost yang tersedia
            $availableKosts = $this->getAvailableKostsContext();
            
            // Buat prompt untuk AI
            $prompt = $this->buildSearchPrompt($query, $availableKosts);
            
            // Kirim request ke Groq AI
            $aiResponse = $this->sendToGroqAI($prompt);
            
            // Parse response AI untuk mendapatkan kriteria pencarian
            $searchCriteria = $this->parseAiResponse($aiResponse);
            
            // Cari kost berdasarkan kriteria yang diparsing
            $results = $this->searchKostsByCriteria($searchCriteria);
            
            $responseTime = microtime(true) - $startTime;
            
            // Log pencarian
            AiSearchLog::logSearch(
                $userId,
                $query,
                $aiResponse,
                count($results),
                $responseTime
            );
            
            return [
                'success' => true,
                'results' => $results,
                'ai_response' => $aiResponse,
                'search_criteria' => $searchCriteria,
                'response_time' => $responseTime,
            ];
            
        } catch (\Exception $e) {
            Log::error('Groq AI Search Error: ' . $e->getMessage());
            
            // Fallback ke pencarian sederhana
            $fallbackResults = $this->fallbackSearch($query);
            
            return [
                'success' => false,
                'error' => 'Terjadi kesalahan pada pencarian AI. Menggunakan pencarian sederhana.',
                'results' => $fallbackResults,
                'ai_response' => null,
                'search_criteria' => null,
                'response_time' => microtime(true) - $startTime,
            ];
        }
    }

    /**
     * Mendapatkan konteks kost yang tersedia untuk AI
     */
    private function getAvailableKostsContext(): string
    {
        $kosts = Kost::with(['facilities', 'images'])
            ->approved()
            ->active()
            ->limit(50) // Batasi untuk menghindari prompt yang terlalu panjang
            ->get();

        $context = "Daftar kost yang tersedia:\n";
        
        foreach ($kosts as $kost) {
            $facilities = $kost->facilities->pluck('name')->join(', ');
            $context .= "- {$kost->name} di {$kost->city}, {$kost->province}\n";
            $context .= "  Harga: {$kost->formatted_price}/bulan\n";
            $context .= "  Tipe: {$kost->gender_type}, {$kost->kost_type}\n";
            $context .= "  Fasilitas: {$facilities}\n";
            $context .= "  Kamar tersedia: {$kost->available_rooms}\n\n";
        }
        
        return $context;
    }

    /**
     * Membuat prompt untuk AI
     */
    private function buildSearchPrompt(string $query, string $kostsContext): string
    {
        return "Anda adalah asisten pencarian kost yang membantu pengguna menemukan kost yang sesuai dengan kebutuhan mereka.

{$kostsContext}

Permintaan pengguna: \"{$query}\"

Berdasarkan permintaan pengguna dan daftar kost yang tersedia, berikan respons dalam format JSON dengan struktur berikut:
{
    \"interpretation\": \"Interpretasi kebutuhan pengguna dalam bahasa Indonesia\",
    \"criteria\": {
        \"city\": \"nama kota atau null\",
        \"max_price\": \"harga maksimum atau null\",
        \"min_price\": \"harga minimum atau null\",
        \"gender_type\": \"putra/putri/campur atau null\",
        \"kost_type\": \"bulanan/harian/keduanya atau null\",
        \"facilities\": [\"daftar fasilitas yang diinginkan atau array kosong\"]
    },
    \"recommendations\": [\"daftar nama kost yang direkomendasikan berdasarkan kriteria\"]
}

Pastikan respons dalam format JSON yang valid dan gunakan bahasa Indonesia yang natural.";
    }

    /**
     * Mengirim request ke Groq AI
     */
    private function sendToGroqAI(string $prompt): string
    {
        try {
            $response = $this->client->post($this->apiUrl . '/chat/completions', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->apiKey,
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'model' => $this->model,
                    'messages' => [
                        [
                            'role' => 'user',
                            'content' => $prompt,
                        ],
                    ],
                    'max_tokens' => $this->maxTokens,
                    'temperature' => $this->temperature,
                ],
                'timeout' => 30,
            ]);

            $data = json_decode($response->getBody()->getContents(), true);
            
            return $data['choices'][0]['message']['content'] ?? '';
            
        } catch (GuzzleException $e) {
            Log::error('Groq API Error: ' . $e->getMessage());
            throw new \Exception('Gagal berkomunikasi dengan AI service');
        }
    }

    /**
     * Parse response AI untuk mendapatkan kriteria pencarian
     */
    private function parseAiResponse(string $aiResponse): array
    {
        try {
            // Coba parse JSON dari response AI
            $decoded = json_decode($aiResponse, true);
            
            if (json_last_error() === JSON_ERROR_NONE && isset($decoded['criteria'])) {
                return $decoded;
            }
            
            // Jika gagal parse JSON, return default criteria
            return [
                'interpretation' => 'Pencarian umum',
                'criteria' => [
                    'city' => null,
                    'max_price' => null,
                    'min_price' => null,
                    'gender_type' => null,
                    'kost_type' => null,
                    'facilities' => [],
                ],
                'recommendations' => [],
            ];
            
        } catch (\Exception $e) {
            Log::error('AI Response Parse Error: ' . $e->getMessage());
            return [
                'interpretation' => 'Pencarian umum',
                'criteria' => [
                    'city' => null,
                    'max_price' => null,
                    'min_price' => null,
                    'gender_type' => null,
                    'kost_type' => null,
                    'facilities' => [],
                ],
                'recommendations' => [],
            ];
        }
    }

    /**
     * Mencari kost berdasarkan kriteria yang diparsing dari AI
     */
    private function searchKostsByCriteria(array $parsedResponse): array
    {
        $criteria = $parsedResponse['criteria'] ?? [];
        
        $query = Kost::with(['facilities', 'images', 'owner'])
            ->approved()
            ->active();

        // Filter berdasarkan kota
        if (!empty($criteria['city'])) {
            $query->byCity($criteria['city']);
        }

        // Filter berdasarkan gender type
        if (!empty($criteria['gender_type'])) {
            $query->byGender($criteria['gender_type']);
        }

        // Filter berdasarkan range harga
        if (!empty($criteria['min_price']) || !empty($criteria['max_price'])) {
            $query->byPriceRange($criteria['min_price'], $criteria['max_price']);
        }

        // Filter berdasarkan fasilitas
        if (!empty($criteria['facilities'])) {
            $query->whereHas('facilities', function ($q) use ($criteria) {
                $q->whereIn('name', $criteria['facilities']);
            });
        }

        return $query->limit(20)->get()->toArray();
    }

    /**
     * Fallback search jika AI gagal
     */
    private function fallbackSearch(string $query): array
    {
        return Kost::with(['facilities', 'images', 'owner'])
            ->approved()
            ->active()
            ->where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%")
                  ->orWhere('city', 'like', "%{$query}%")
                  ->orWhere('address', 'like', "%{$query}%");
            })
            ->limit(20)
            ->get()
            ->toArray();
    }

    /**
     * Cek apakah service tersedia
     */
    public function isAvailable(): bool
    {
        return !empty($this->apiKey);
    }
}
