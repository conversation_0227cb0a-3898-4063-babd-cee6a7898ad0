<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Inquiry extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'kost_id',
        'user_id',
        'message',
        'contact_preference',
        'status',
        'responded_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected function casts(): array
    {
        return [
            'responded_at' => 'datetime',
        ];
    }

    /**
     * Konstanta untuk status inquiry
     */
    public const STATUS_PENDING = 'pending';
    public const STATUS_RESPONDED = 'responded';
    public const STATUS_CLOSED = 'closed';

    /**
     * Konstanta untuk contact preference
     */
    public const CONTACT_EMAIL = 'email';
    public const CONTACT_PHONE = 'phone';
    public const CONTACT_WHATSAPP = 'whatsapp';

    /**
     * Mendapatkan semua status yang tersedia
     */
    public static function getStatuses(): array
    {
        return [
            self::STATUS_PENDING => 'Menunggu Respon',
            self::STATUS_RESPONDED => 'Sudah Direspon',
            self::STATUS_CLOSED => 'Ditutup',
        ];
    }

    /**
     * Mendapatkan semua contact preference yang tersedia
     */
    public static function getContactPreferences(): array
    {
        return [
            self::CONTACT_EMAIL => 'Email',
            self::CONTACT_PHONE => 'Telepon',
            self::CONTACT_WHATSAPP => 'WhatsApp',
        ];
    }

    /**
     * Relationship: Inquiry milik satu kost
     */
    public function kost(): BelongsTo
    {
        return $this->belongsTo(Kost::class);
    }

    /**
     * Relationship: Inquiry dibuat oleh satu user
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope: Filter berdasarkan status
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope: Filter inquiry yang pending
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope: Filter inquiry yang sudah direspon
     */
    public function scopeResponded($query)
    {
        return $query->where('status', self::STATUS_RESPONDED);
    }

    /**
     * Scope: Filter berdasarkan pemilik kost
     */
    public function scopeForOwner($query, int $ownerId)
    {
        return $query->whereHas('kost', function ($q) use ($ownerId) {
            $q->where('owner_id', $ownerId);
        });
    }

    /**
     * Tandai inquiry sebagai sudah direspon
     */
    public function markAsResponded(): void
    {
        $this->update([
            'status' => self::STATUS_RESPONDED,
            'responded_at' => now(),
        ]);
    }

    /**
     * Tutup inquiry
     */
    public function close(): void
    {
        $this->update(['status' => self::STATUS_CLOSED]);
    }

    /**
     * Mendapatkan nama status yang diformat
     */
    public function getStatusNameAttribute(): string
    {
        $statuses = self::getStatuses();
        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * Mendapatkan nama contact preference yang diformat
     */
    public function getContactPreferenceNameAttribute(): string
    {
        $preferences = self::getContactPreferences();
        return $preferences[$this->contact_preference] ?? $this->contact_preference;
    }
}
