// Test script untuk workflow Pencari Kost
const BASE_URL = 'http://localhost:8000';

// Helper function untuk HTTP requests
async function makeRequest(url, options = {}) {
    try {
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                ...options.headers
            },
            ...options
        });
        
        const data = await response.json();
        return { status: response.status, data, ok: response.ok };
    } catch (error) {
        console.error('Request failed:', error);
        return { status: 0, data: { error: error.message }, ok: false };
    }
}

let pencariToken = '';

async function loginAsPencari() {
    console.log('\n=== Login as Pencari Kost ===');
    
    const loginResult = await makeRequest(`${BASE_URL}/api/login`, {
        method: 'POST',
        body: JSON.stringify({
            email: '<EMAIL>',
            password: 'password123'
        })
    });
    
    console.log('Login Status:', loginResult.status);
    if (loginResult.ok && loginResult.data.success) {
        pencariToken = loginResult.data.data.token;
        console.log('✅ Login successful, token obtained');
        return true;
    } else {
        console.log('❌ Login failed:', loginResult.data);
        return false;
    }
}

async function testBrowseAllKosts() {
    console.log('\n=== Testing Browse All Kosts ===');
    
    const result = await makeRequest(`${BASE_URL}/api/kosts`);
    
    console.log('Browse Kosts Status:', result.status);
    console.log('Total Kosts:', result.data?.data?.length || 0);
    
    if (result.data?.data?.length > 0) {
        const firstKost = result.data.data[0];
        console.log('Sample Kost:', {
            id: firstKost.id,
            name: firstKost.name,
            city: firstKost.city,
            price_monthly: firstKost.price_monthly,
            gender_type: firstKost.gender_type,
            status: firstKost.status
        });
    }
    
    return result;
}

async function testSearchWithFilters() {
    console.log('\n=== Testing Search with Filters ===');
    
    // Test filter by city
    console.log('\n1. Filter by City (Jakarta)...');
    const cityFilter = await makeRequest(`${BASE_URL}/api/kosts?city=Jakarta`);
    console.log('Jakarta Kosts:', cityFilter.data?.data?.length || 0);
    
    // Test filter by gender type
    console.log('\n2. Filter by Gender Type (putra)...');
    const genderFilter = await makeRequest(`${BASE_URL}/api/kosts?gender_type=putra`);
    console.log('Putra Kosts:', genderFilter.data?.data?.length || 0);
    
    // Test filter by price range
    console.log('\n3. Filter by Price Range (1000000-2000000)...');
    const priceFilter = await makeRequest(`${BASE_URL}/api/kosts?min_price=1000000&max_price=2000000`);
    console.log('Price Range Kosts:', priceFilter.data?.data?.length || 0);
    
    // Test combined filters
    console.log('\n4. Combined Filters (Jakarta + Putra + Price Range)...');
    const combinedFilter = await makeRequest(`${BASE_URL}/api/kosts?city=Jakarta&gender_type=putra&min_price=1000000&max_price=2000000`);
    console.log('Combined Filter Kosts:', combinedFilter.data?.data?.length || 0);
    
    return { cityFilter, genderFilter, priceFilter, combinedFilter };
}

async function testGetKostDetails() {
    console.log('\n=== Testing Get Kost Details ===');
    
    // Get first kost ID
    const allKosts = await makeRequest(`${BASE_URL}/api/kosts`);
    if (allKosts.data?.data?.length > 0) {
        const kostId = allKosts.data.data[0].id;
        
        const kostDetail = await makeRequest(`${BASE_URL}/api/kosts/${kostId}`);
        console.log('Kost Detail Status:', kostDetail.status);
        
        if (kostDetail.ok) {
            const kost = kostDetail.data.data;
            console.log('Kost Details:', {
                id: kost.id,
                name: kost.name,
                description: kost.description?.substring(0, 100) + '...',
                facilities: kost.facilities?.map(f => f.name) || [],
                images_count: kost.images?.length || 0
            });
            
            // Test get similar kosts
            console.log('\n--- Testing Similar Kosts ---');
            const similarKosts = await makeRequest(`${BASE_URL}/api/kosts/${kostId}/similar`);
            console.log('Similar Kosts Status:', similarKosts.status);
            console.log('Similar Kosts Count:', similarKosts.data?.data?.length || 0);
        }
        
        return kostDetail;
    } else {
        console.log('❌ No kosts available for detail testing');
        return null;
    }
}

async function testPopularAndLatestKosts() {
    console.log('\n=== Testing Popular and Latest Kosts ===');
    
    // Test popular kosts
    console.log('\n1. Testing Popular Kosts...');
    const popularKosts = await makeRequest(`${BASE_URL}/api/kosts/popular`);
    console.log('Popular Kosts Status:', popularKosts.status);
    console.log('Popular Kosts Count:', popularKosts.data?.data?.length || 0);
    
    // Test latest kosts
    console.log('\n2. Testing Latest Kosts...');
    const latestKosts = await makeRequest(`${BASE_URL}/api/kosts/latest`);
    console.log('Latest Kosts Status:', latestKosts.status);
    console.log('Latest Kosts Count:', latestKosts.data?.data?.length || 0);
    
    return { popularKosts, latestKosts };
}

async function testAISearch() {
    console.log('\n=== Testing AI Search ===');
    
    const searchQueries = [
        'Cari kost murah di Jakarta untuk mahasiswa',
        'Kost dekat kampus dengan wifi dan AC',
        'Kost putri yang aman dan nyaman'
    ];
    
    for (const query of searchQueries) {
        console.log(`\n--- Testing AI Search: "${query}" ---`);
        
        const aiSearchResult = await makeRequest(`${BASE_URL}/api/ai-search/search`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${pencariToken}`
            },
            body: JSON.stringify({ query })
        });
        
        console.log('AI Search Status:', aiSearchResult.status);
        if (aiSearchResult.ok) {
            console.log('AI Search Results:', aiSearchResult.data?.data?.results?.length || 0);
            console.log('AI Interpretation:', aiSearchResult.data?.data?.interpretation || 'N/A');
        } else {
            console.log('AI Search Error:', aiSearchResult.data);
        }
    }
}

async function testCreateInquiry() {
    console.log('\n=== Testing Create Inquiry ===');
    
    // Get first available kost
    const allKosts = await makeRequest(`${BASE_URL}/api/kosts`);
    if (allKosts.data?.data?.length > 0) {
        const kostId = allKosts.data.data[0].id;
        
        const inquiryData = {
            message: 'Halo, saya tertarik dengan kost ini. Apakah masih ada kamar yang tersedia? Mohon info lebih lanjut.',
            contact_preference: 'whatsapp'
        };
        
        const inquiryResult = await makeRequest(`${BASE_URL}/api/inquiries`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${pencariToken}`
            },
            body: JSON.stringify({
                kost_id: kostId,
                ...inquiryData
            })
        });
        
        console.log('Create Inquiry Status:', inquiryResult.status);
        console.log('Create Inquiry Response:', JSON.stringify(inquiryResult.data, null, 2));
        
        return inquiryResult;
    } else {
        console.log('❌ No kosts available for inquiry testing');
        return null;
    }
}

// Main test runner
async function runPencariWorkflowTests() {
    console.log('🔍 Starting Pencari Kost Workflow Testing...');
    console.log('Base URL:', BASE_URL);
    
    try {
        // Login as pencari
        const loginSuccess = await loginAsPencari();
        if (!loginSuccess) {
            console.log('❌ Cannot proceed without login');
            return;
        }
        
        // Test browse all kosts
        await testBrowseAllKosts();
        
        // Test search with filters
        await testSearchWithFilters();
        
        // Test get kost details
        await testGetKostDetails();
        
        // Test popular and latest kosts
        await testPopularAndLatestKosts();
        
        // Test AI search
        await testAISearch();
        
        // Test create inquiry
        await testCreateInquiry();
        
        console.log('\n=== Pencari Workflow Test Summary ===');
        console.log('✅ All tests completed!');
        console.log('📝 Note: Web interface testing would include UI interactions');
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

// Run tests
runPencariWorkflowTests();
