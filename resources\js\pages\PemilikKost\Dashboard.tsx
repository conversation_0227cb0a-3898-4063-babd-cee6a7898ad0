import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import KostCard from '@/components/kost/kost-card';
import AppLayout from '@/layouts/app-layout';
import { Inquiry, Kost, Notification, PageProps } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { 
    Plus, 
    Home, 
    MessageCircle, 
    Bell, 
    CheckCircle, 
    Clock, 
    AlertTriangle,
    TrendingUp,
    Eye,
    Edit,
    Send,
    Users
} from 'lucide-react';
import React from 'react';

interface DashboardProps extends PageProps {
    stats: {
        total_kosts: number;
        approved_kosts: number;
        pending_kosts: number;
        total_inquiries: number;
        pending_inquiries: number;
        unread_notifications: number;
    };
    myKosts: Kost[];
    recentInquiries: Inquiry[];
    recentNotifications: Notification[];
}

export default function Dashboard({
    auth,
    stats,
    myKosts,
    recentInquiries,
    recentNotifications,
}: DashboardProps) {
    const handleViewKost = (kost: Kost) => {
        router.visit(route('pemilik.kosts.show', kost.id));
    };

    const handleEditKost = (kost: Kost) => {
        router.visit(route('pemilik.kosts.edit', kost.id));
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'approved': return 'bg-green-100 text-green-800';
            case 'pending': return 'bg-yellow-100 text-yellow-800';
            case 'rejected': return 'bg-red-100 text-red-800';
            case 'draft': return 'bg-gray-100 text-gray-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'approved': return <CheckCircle className="h-4 w-4" />;
            case 'pending': return <Clock className="h-4 w-4" />;
            case 'rejected': return <AlertTriangle className="h-4 w-4" />;
            case 'draft': return <Edit className="h-4 w-4" />;
            default: return <Clock className="h-4 w-4" />;
        }
    };

    const getInquiryStatusColor = (status: string) => {
        switch (status) {
            case 'pending': return 'bg-yellow-100 text-yellow-800';
            case 'responded': return 'bg-green-100 text-green-800';
            case 'closed': return 'bg-gray-100 text-gray-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    return (
        <AppLayout>
            <Head title="Dashboard Pemilik Kost" />

            <div className="space-y-8">
                {/* Welcome Section */}
                <div className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg p-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-2xl font-bold mb-2">
                                Selamat datang, {auth.user.name}! 🏠
                            </h1>
                            <p className="text-muted-foreground">
                                Kelola kost Anda dan pantau inquiry dari calon penyewa.
                            </p>
                        </div>
                        <Button asChild size="lg">
                            <Link href={route('pemilik.kosts.create')}>
                                <Plus className="h-5 w-5 mr-2" />
                                Tambah Kost Baru
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Kost</CardTitle>
                            <Home className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_kosts}</div>
                            <p className="text-xs text-muted-foreground">
                                Kost yang Anda miliki
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Disetujui</CardTitle>
                            <CheckCircle className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.approved_kosts}</div>
                            <p className="text-xs text-muted-foreground">
                                Kost aktif
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Pending</CardTitle>
                            <Clock className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.pending_kosts}</div>
                            <p className="text-xs text-muted-foreground">
                                Menunggu review
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Inquiry</CardTitle>
                            <MessageCircle className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_inquiries}</div>
                            <p className="text-xs text-muted-foreground">
                                Inquiry diterima
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Inquiry Baru</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.pending_inquiries}</div>
                            <p className="text-xs text-muted-foreground">
                                Belum direspon
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Notifikasi</CardTitle>
                            <Bell className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.unread_notifications}</div>
                            <p className="text-xs text-muted-foreground">
                                Belum dibaca
                            </p>
                        </CardContent>
                    </Card>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* My Kosts */}
                    <div className="lg:col-span-2 space-y-6">
                        <div className="flex items-center justify-between">
                            <h2 className="text-xl font-semibold flex items-center gap-2">
                                <Home className="h-5 w-5" />
                                Kost Saya
                            </h2>
                            <Button variant="outline" asChild>
                                <Link href={route('pemilik.kosts.index')}>
                                    <Eye className="h-4 w-4 mr-2" />
                                    Lihat Semua
                                </Link>
                            </Button>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {myKosts.map((kost) => (
                                <div key={kost.id} className="relative">
                                    <KostCard
                                        kost={kost}
                                        onView={handleViewKost}
                                        showOwnerInfo={false}
                                    />
                                    {/* Status Badge */}
                                    <div className="absolute top-2 left-2">
                                        <Badge className={getStatusColor(kost.status)}>
                                            <div className="flex items-center gap-1">
                                                {getStatusIcon(kost.status)}
                                                <span className="text-xs capitalize">{kost.status}</span>
                                            </div>
                                        </Badge>
                                    </div>
                                    {/* Action Buttons */}
                                    <div className="absolute top-2 right-2 flex gap-2">
                                        <Button
                                            size="sm"
                                            variant="secondary"
                                            onClick={() => handleEditKost(kost)}
                                        >
                                            <Edit className="h-3 w-3" />
                                        </Button>
                                        {kost.status === 'draft' && (
                                            <Button
                                                size="sm"
                                                onClick={() => router.post(route('pemilik.kosts.submit', kost.id))}
                                            >
                                                <Send className="h-3 w-3" />
                                            </Button>
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>

                        {myKosts.length === 0 && (
                            <Card>
                                <CardContent className="flex items-center justify-center py-12">
                                    <div className="text-center text-muted-foreground">
                                        <Home className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                        <h3 className="text-lg font-medium mb-2">Belum ada kost</h3>
                                        <p className="mb-4">Mulai dengan menambahkan kost pertama Anda</p>
                                        <Button asChild>
                                            <Link href={route('pemilik.kosts.create')}>
                                                <Plus className="h-4 w-4 mr-2" />
                                                Tambah Kost Baru
                                            </Link>
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        )}
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        {/* Recent Inquiries */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <MessageCircle className="h-5 w-5" />
                                    Inquiry Terbaru
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {recentInquiries.length > 0 ? (
                                    <>
                                        {recentInquiries.map((inquiry) => (
                                            <div key={inquiry.id} className="space-y-2">
                                                <div className="flex items-start justify-between">
                                                    <div className="flex-1 min-w-0">
                                                        <p className="font-medium text-sm truncate">
                                                            {inquiry.user?.name}
                                                        </p>
                                                        <p className="text-xs text-muted-foreground">
                                                            {inquiry.kost?.name}
                                                        </p>
                                                        <p className="text-xs text-muted-foreground">
                                                            {new Date(inquiry.created_at).toLocaleDateString('id-ID')}
                                                        </p>
                                                    </div>
                                                    <Badge className={getInquiryStatusColor(inquiry.status)}>
                                                        <span className="text-xs">{inquiry.status_name}</span>
                                                    </Badge>
                                                </div>
                                                <p className="text-sm text-muted-foreground line-clamp-2">
                                                    {inquiry.message}
                                                </p>
                                            </div>
                                        ))}
                                        <Separator />
                                        <Button variant="outline" size="sm" className="w-full" asChild>
                                            <Link href={route('pemilik.inquiries')}>
                                                Lihat Semua Inquiry
                                            </Link>
                                        </Button>
                                    </>
                                ) : (
                                    <div className="text-center text-muted-foreground py-4">
                                        <MessageCircle className="h-6 w-6 mx-auto mb-2 opacity-50" />
                                        <p className="text-sm">Belum ada inquiry</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Recent Notifications */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Bell className="h-5 w-5" />
                                    Notifikasi Terbaru
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {recentNotifications.length > 0 ? (
                                    <>
                                        {recentNotifications.map((notification) => (
                                            <div key={notification.id} className="space-y-1">
                                                <div className="flex items-start justify-between">
                                                    <p className="font-medium text-sm">{notification.title}</p>
                                                    {!notification.read_at && (
                                                        <div className="h-2 w-2 bg-primary rounded-full flex-shrink-0 mt-1" />
                                                    )}
                                                </div>
                                                <p className="text-sm text-muted-foreground line-clamp-2">
                                                    {notification.message}
                                                </p>
                                                <p className="text-xs text-muted-foreground">
                                                    {new Date(notification.created_at).toLocaleDateString('id-ID')}
                                                </p>
                                            </div>
                                        ))}
                                        <Separator />
                                        <Button variant="outline" size="sm" className="w-full" asChild>
                                            <Link href={route('pemilik.notifications')}>
                                                Lihat Semua Notifikasi
                                            </Link>
                                        </Button>
                                    </>
                                ) : (
                                    <div className="text-center text-muted-foreground py-4">
                                        <Bell className="h-6 w-6 mx-auto mb-2 opacity-50" />
                                        <p className="text-sm">Belum ada notifikasi</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
