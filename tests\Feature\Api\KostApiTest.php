<?php

namespace Tests\Feature\Api;

use App\Models\Kost;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class KostApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed();
    }

    public function test_can_get_kosts_list(): void
    {
        // Create test data
        $owner = User::factory()->create(['role' => User::ROLE_PEMILIK_KOST]);
        $kost = Kost::factory()->create([
            'owner_id' => $owner->id,
            'status' => 'approved',
            'is_active' => true,
        ]);

        $response = $this->getJson('/api/kosts');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'description',
                            'city',
                            'province',
                            'price_monthly',
                            'status',
                            'is_active',
                        ]
                    ],
                    'links',
                    'meta',
                ]);
    }

    public function test_can_get_single_kost(): void
    {
        $owner = User::factory()->create(['role' => User::ROLE_PEMILIK_KOST]);
        $kost = Kost::factory()->create([
            'owner_id' => $owner->id,
            'status' => 'approved',
            'is_active' => true,
        ]);

        $response = $this->getJson("/api/kosts/{$kost->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'id',
                    'name',
                    'description',
                    'city',
                    'province',
                    'price_monthly',
                    'status',
                    'is_active',
                    'owner',
                ]);
    }

    public function test_cannot_get_inactive_kost(): void
    {
        $owner = User::factory()->create(['role' => User::ROLE_PEMILIK_KOST]);
        $kost = Kost::factory()->create([
            'owner_id' => $owner->id,
            'status' => 'draft',
            'is_active' => false,
        ]);

        $response = $this->getJson("/api/kosts/{$kost->id}");

        $response->assertStatus(404);
    }

    public function test_can_filter_kosts_by_city(): void
    {
        $owner = User::factory()->create(['role' => User::ROLE_PEMILIK_KOST]);

        $kostJakarta = Kost::factory()->create([
            'owner_id' => $owner->id,
            'city' => 'Jakarta',
            'status' => 'approved',
            'is_active' => true,
        ]);

        $kostBandung = Kost::factory()->create([
            'owner_id' => $owner->id,
            'city' => 'Bandung',
            'status' => 'approved',
            'is_active' => true,
        ]);

        $response = $this->getJson('/api/kosts?city=Jakarta');

        $response->assertStatus(200);

        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals('Jakarta', $data[0]['city']);
    }

    public function test_authenticated_user_can_get_my_kosts(): void
    {
        $owner = User::factory()->create(['role' => User::ROLE_PEMILIK_KOST]);
        $otherOwner = User::factory()->create(['role' => User::ROLE_PEMILIK_KOST]);

        $myKost = Kost::factory()->create(['owner_id' => $owner->id]);
        $otherKost = Kost::factory()->create(['owner_id' => $otherOwner->id]);

        Sanctum::actingAs($owner);

        $response = $this->getJson('/api/my-kosts');

        $response->assertStatus(200);

        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($myKost->id, $data[0]['id']);
    }

    public function test_non_owner_cannot_get_my_kosts(): void
    {
        $user = User::factory()->create(['role' => User::ROLE_PENCARI_KOST]);

        Sanctum::actingAs($user);

        $response = $this->getJson('/api/my-kosts');

        $response->assertStatus(403);
    }

    public function test_can_get_popular_kosts(): void
    {
        $response = $this->getJson('/api/kosts/popular');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'city',
                            'price_monthly',
                        ]
                    ]
                ]);
    }

    public function test_can_get_cities_list(): void
    {
        $response = $this->getJson('/api/kosts/cities');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data'
                ]);
    }
}
