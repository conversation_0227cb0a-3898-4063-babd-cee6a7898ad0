<?php

namespace Tests\Feature\Api;

use App\Models\AiSearchLog;
use App\Models\User;
use App\Services\GroqAiService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Lara<PERSON>\Sanctum\Sanctum;
use Mockery;
use Tests\TestCase;

class AiSearchTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed();
    }

    public function test_authenticated_user_can_search_with_ai(): void
    {
        $user = User::factory()->create(['role' => User::ROLE_PENCARI_KOST]);
        Sanctum::actingAs($user);

        // Mock GroqAiService
        $mockService = Mockery::mock(GroqAiService::class);
        $mockService->shouldReceive('isAvailable')->andReturn(true);
        $mockService->shouldReceive('searchKosts')
            ->with('cari kost di Jakarta', $user->id)
            ->andReturn([
                'success' => true,
                'results' => [],
                'ai_response' => 'Mencari kost di Jakarta...',
                'search_criteria' => [
                    'interpretation' => 'User mencari kost di Jakarta',
                    'criteria' => ['city' => 'Jakarta'],
                    'recommendations' => [],
                ],
                'response_time' => 1.5,
            ]);

        $this->app->instance(GroqAiService::class, $mockService);

        $response = $this->postJson('/api/ai-search/search', [
            'query' => 'cari kost di Jakarta'
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'query',
                        'results',
                        'ai_interpretation',
                        'search_criteria',
                        'recommendations',
                        'response_time',
                        'total_results',
                    ]
                ]);
    }

    public function test_unauthenticated_user_cannot_search_with_ai(): void
    {
        $response = $this->postJson('/api/ai-search/search', [
            'query' => 'cari kost di Jakarta'
        ]);

        $response->assertStatus(401);
    }

    public function test_search_requires_valid_query(): void
    {
        $user = User::factory()->create(['role' => User::ROLE_PENCARI_KOST]);
        Sanctum::actingAs($user);

        // Test empty query
        $response = $this->postJson('/api/ai-search/search', [
            'query' => ''
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['query']);
    }

    public function test_can_get_search_history(): void
    {
        $user = User::factory()->create(['role' => User::ROLE_PENCARI_KOST]);
        Sanctum::actingAs($user);

        $response = $this->getJson('/api/ai-search/history');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data'
                ]);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
