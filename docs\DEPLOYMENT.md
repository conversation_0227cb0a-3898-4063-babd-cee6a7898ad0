# 🚀 SIM Kost Deployment Guide

## Production Deployment Checklist

### 1. Server Requirements

#### Minimum System Requirements
- **OS**: Ubuntu 20.04+ / CentOS 8+ / Amazon Linux 2
- **CPU**: 2 cores
- **RAM**: 4GB
- **Storage**: 20GB SSD
- **PHP**: 8.2+
- **Node.js**: 18+
- **MySQL**: 8.0+
- **Nginx**: 1.18+

#### Recommended System Requirements
- **OS**: Ubuntu 22.04 LTS
- **CPU**: 4 cores
- **RAM**: 8GB
- **Storage**: 50GB SSD
- **PHP**: 8.3
- **Node.js**: 20 LTS
- **MySQL**: 8.0
- **Nginx**: 1.22+
- **Redis**: 7.0+ (for caching and sessions)

### 2. Server Setup

#### Install Dependencies (Ubuntu 22.04)
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install PHP and extensions
sudo apt install -y php8.3 php8.3-fpm php8.3-mysql php8.3-xml php8.3-curl \
    php8.3-gd php8.3-mbstring php8.3-zip php8.3-intl php8.3-bcmath \
    php8.3-redis php8.3-imagick

# Install MySQL
sudo apt install -y mysql-server

# Install Nginx
sudo apt install -y nginx

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt install -y nodejs

# Install Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer

# Install Redis (optional but recommended)
sudo apt install -y redis-server

# Install Supervisor (for queue workers)
sudo apt install -y supervisor
```

### 3. Database Setup

```bash
# Secure MySQL installation
sudo mysql_secure_installation

# Create database and user
sudo mysql -u root -p
```

```sql
CREATE DATABASE sim_kost CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'simkost_user'@'localhost' IDENTIFIED BY 'secure_password_here';
GRANT ALL PRIVILEGES ON sim_kost.* TO 'simkost_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 4. Application Deployment

#### Clone and Setup Application
```bash
# Create application directory
sudo mkdir -p /var/www/sim-kost
cd /var/www/sim-kost

# Clone repository
sudo git clone https://github.com/your-username/sim-kost.git .

# Set ownership
sudo chown -R www-data:www-data /var/www/sim-kost
sudo chmod -R 755 /var/www/sim-kost

# Install PHP dependencies
sudo -u www-data composer install --optimize-autoloader --no-dev

# Install Node.js dependencies
sudo -u www-data npm ci --only=production

# Copy environment file
sudo -u www-data cp .env.example .env
```

#### Configure Environment
```bash
# Edit environment file
sudo -u www-data nano .env
```

```env
APP_NAME="SIM Kost"
APP_ENV=production
APP_KEY=base64:your-generated-key-here
APP_DEBUG=false
APP_URL=https://your-domain.com

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=sim_kost
DB_USERNAME=simkost_user
DB_PASSWORD=secure_password_here

# Cache Configuration
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Redis Configuration
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# AI Configuration
GROQ_API_KEY=your_groq_api_key_here
GROQ_MODEL=llama3-8b-8192
GROQ_MAX_TOKENS=8192
GROQ_TEMPERATURE=1

# Broadcasting (for real-time features)
BROADCAST_DRIVER=pusher
PUSHER_APP_ID=your_pusher_app_id
PUSHER_APP_KEY=your_pusher_key
PUSHER_APP_SECRET=your_pusher_secret
PUSHER_APP_CLUSTER=your_cluster
```

#### Generate Application Key and Setup
```bash
# Generate application key
sudo -u www-data php artisan key:generate

# Run migrations
sudo -u www-data php artisan migrate --force

# Seed database (optional)
sudo -u www-data php artisan db:seed --force

# Create storage link
sudo -u www-data php artisan storage:link

# Build frontend assets
sudo -u www-data npm run build

# Cache configuration
sudo -u www-data php artisan config:cache
sudo -u www-data php artisan route:cache
sudo -u www-data php artisan view:cache

# Set proper permissions
sudo chown -R www-data:www-data /var/www/sim-kost
sudo chmod -R 755 /var/www/sim-kost
sudo chmod -R 775 /var/www/sim-kost/storage
sudo chmod -R 775 /var/www/sim-kost/bootstrap/cache
```

### 5. Nginx Configuration

```bash
# Create Nginx configuration
sudo nano /etc/nginx/sites-available/sim-kost
```

```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    root /var/www/sim-kost/public;

    # SSL Configuration
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    index index.php;

    charset utf-8;

    # Handle Laravel routes
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # Handle PHP files
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.3-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Deny access to hidden files
    location ~ /\.(?!well-known).* {
        deny all;
    }

    # Handle static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }

    # File upload size
    client_max_body_size 10M;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript;
}
```

```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/sim-kost /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 6. SSL Certificate Setup

#### Using Let's Encrypt (Recommended)
```bash
# Install Certbot
sudo apt install -y certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Auto-renewal (already set up by certbot)
sudo crontab -l | grep certbot
```

### 7. Queue Workers Setup

```bash
# Create Supervisor configuration
sudo nano /etc/supervisor/conf.d/sim-kost-worker.conf
```

```ini
[program:sim-kost-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/sim-kost/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/www/sim-kost/storage/logs/worker.log
stopwaitsecs=3600
```

```bash
# Update Supervisor
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start sim-kost-worker:*
```

### 8. Cron Jobs Setup

```bash
# Edit crontab for www-data user
sudo crontab -u www-data -e
```

```cron
# Laravel Scheduler
* * * * * cd /var/www/sim-kost && php artisan schedule:run >> /dev/null 2>&1

# Backup database daily at 2 AM
0 2 * * * cd /var/www/sim-kost && php artisan backup:run >> /dev/null 2>&1

# Cleanup old logs weekly
0 0 * * 0 cd /var/www/sim-kost && php artisan log:clear >> /dev/null 2>&1
```

### 9. Monitoring and Logging

#### Setup Log Rotation
```bash
sudo nano /etc/logrotate.d/sim-kost
```

```
/var/www/sim-kost/storage/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    notifempty
    create 644 www-data www-data
    postrotate
        sudo systemctl reload php8.3-fpm
    endscript
}
```

#### Setup Monitoring (Optional)
```bash
# Install monitoring tools
sudo apt install -y htop iotop nethogs

# Setup basic monitoring script
sudo nano /usr/local/bin/sim-kost-monitor.sh
```

```bash
#!/bin/bash
# Basic monitoring script for SIM Kost

# Check if services are running
services=("nginx" "mysql" "php8.3-fpm" "redis-server")
for service in "${services[@]}"; do
    if ! systemctl is-active --quiet $service; then
        echo "$(date): $service is not running" >> /var/log/sim-kost-monitor.log
        systemctl restart $service
    fi
done

# Check disk space
disk_usage=$(df /var/www/sim-kost | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $disk_usage -gt 80 ]; then
    echo "$(date): Disk usage is ${disk_usage}%" >> /var/log/sim-kost-monitor.log
fi
```

```bash
# Make executable and add to cron
sudo chmod +x /usr/local/bin/sim-kost-monitor.sh
sudo crontab -e
```

```cron
# Monitor services every 5 minutes
*/5 * * * * /usr/local/bin/sim-kost-monitor.sh
```

### 10. Security Hardening

#### Firewall Setup
```bash
# Install and configure UFW
sudo ufw enable
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw status
```

#### Additional Security
```bash
# Disable PHP functions (in php.ini)
sudo nano /etc/php/8.3/fpm/php.ini
```

```ini
disable_functions = exec,passthru,shell_exec,system,proc_open,popen,curl_exec,curl_multi_exec,parse_ini_file,show_source
```

```bash
# Restart PHP-FPM
sudo systemctl restart php8.3-fpm
```

### 11. Backup Strategy

#### Database Backup Script
```bash
sudo nano /usr/local/bin/backup-sim-kost.sh
```

```bash
#!/bin/bash
BACKUP_DIR="/var/backups/sim-kost"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="sim_kost"
DB_USER="simkost_user"
DB_PASS="secure_password_here"

# Create backup directory
mkdir -p $BACKUP_DIR

# Database backup
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME | gzip > $BACKUP_DIR/database_$DATE.sql.gz

# Files backup
tar -czf $BACKUP_DIR/files_$DATE.tar.gz -C /var/www/sim-kost storage public/storage

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*.gz" -mtime +7 -delete

echo "Backup completed: $DATE"
```

```bash
# Make executable
sudo chmod +x /usr/local/bin/backup-sim-kost.sh

# Add to cron (daily at 3 AM)
sudo crontab -e
```

```cron
0 3 * * * /usr/local/bin/backup-sim-kost.sh >> /var/log/sim-kost-backup.log 2>&1
```

### 12. Performance Optimization

#### PHP-FPM Optimization
```bash
sudo nano /etc/php/8.3/fpm/pool.d/www.conf
```

```ini
; Process manager settings
pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 500

; Memory settings
php_admin_value[memory_limit] = 256M
php_admin_value[upload_max_filesize] = 10M
php_admin_value[post_max_size] = 10M
```

#### MySQL Optimization
```bash
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf
```

```ini
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
query_cache_type = 1
query_cache_size = 64M
```

```bash
# Restart services
sudo systemctl restart php8.3-fpm
sudo systemctl restart mysql
```

### 13. Final Checks

```bash
# Check all services
sudo systemctl status nginx php8.3-fpm mysql redis-server

# Check queue workers
sudo supervisorctl status

# Check application
curl -I https://your-domain.com

# Check logs
tail -f /var/www/sim-kost/storage/logs/laravel.log
```

## Troubleshooting

### Common Issues

1. **Permission Issues**
   ```bash
   sudo chown -R www-data:www-data /var/www/sim-kost
   sudo chmod -R 755 /var/www/sim-kost
   sudo chmod -R 775 /var/www/sim-kost/storage /var/www/sim-kost/bootstrap/cache
   ```

2. **Queue Not Processing**
   ```bash
   sudo supervisorctl restart sim-kost-worker:*
   ```

3. **High Memory Usage**
   ```bash
   # Clear caches
   php artisan cache:clear
   php artisan config:clear
   php artisan route:clear
   php artisan view:clear
   ```

4. **SSL Issues**
   ```bash
   sudo certbot renew --dry-run
   ```

## Maintenance

### Regular Maintenance Tasks

1. **Weekly**
   - Check logs for errors
   - Monitor disk space
   - Review performance metrics

2. **Monthly**
   - Update system packages
   - Review security logs
   - Test backup restoration

3. **Quarterly**
   - Update application dependencies
   - Review and update SSL certificates
   - Performance optimization review

---

For deployment support, contact <EMAIL>
