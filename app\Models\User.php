<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasApiTokens;

    // Role constants
    public const ROLE_ADMIN = 'admin';
    public const ROLE_PEMILIK_KOST = 'pemilik_kost';
    public const ROLE_PENCARI_KOST = 'pencari_kost';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'phone',
        'avatar',
        'is_active',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
        ];
    }



    /**
     * Mendapatkan semua role yang tersedia
     */
    public static function getRoles(): array
    {
        return [
            self::ROLE_PENCARI_KOST,
            self::ROLE_PEMILIK_KOST,
            self::ROLE_ADMIN,
        ];
    }

    /**
     * Cek apakah user adalah pencari kost
     */
    public function isPencariKost(): bool
    {
        return $this->role === self::ROLE_PENCARI_KOST;
    }

    /**
     * Cek apakah user adalah pemilik kost
     */
    public function isPemilikKost(): bool
    {
        return $this->role === self::ROLE_PEMILIK_KOST;
    }

    /**
     * Cek apakah user adalah admin
     */
    public function isAdmin(): bool
    {
        return $this->role === self::ROLE_ADMIN;
    }

    /**
     * Relationship: User memiliki banyak kost (untuk pemilik kost)
     */
    public function kosts(): HasMany
    {
        return $this->hasMany(Kost::class, 'owner_id');
    }

    /**
     * Relationship: User memiliki banyak inquiry (untuk pencari kost)
     */
    public function inquiries(): HasMany
    {
        return $this->hasMany(Inquiry::class);
    }

    /**
     * Relationship: User memiliki banyak notifikasi
     */
    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class);
    }

    /**
     * Relationship: User memiliki banyak log pencarian AI
     */
    public function aiSearchLogs(): HasMany
    {
        return $this->hasMany(AiSearchLog::class);
    }

    /**
     * Scope: Filter berdasarkan role
     */
    public function scopeByRole($query, string $role)
    {
        return $query->where('role', $role);
    }

    /**
     * Scope: Filter user yang aktif
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
