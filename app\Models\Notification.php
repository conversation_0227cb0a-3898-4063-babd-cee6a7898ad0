<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Notification extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'title',
        'message',
        'type',
        'data',
        'read_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected function casts(): array
    {
        return [
            'data' => 'array',
            'read_at' => 'datetime',
        ];
    }

    /**
     * Konstanta untuk tipe notifikasi
     */
    public const TYPE_INQUIRY = 'inquiry';
    public const TYPE_KOST_APPROVED = 'kost_approved';
    public const TYPE_KOST_REJECTED = 'kost_rejected';
    public const TYPE_SYSTEM = 'system';

    /**
     * Mendapatkan semua tipe notifikasi yang tersedia
     */
    public static function getTypes(): array
    {
        return [
            self::TYPE_INQUIRY => 'Inquiry Baru',
            self::TYPE_KOST_APPROVED => 'Kost Disetujui',
            self::TYPE_KOST_REJECTED => 'Kost Ditolak',
            self::TYPE_SYSTEM => 'Notifikasi Sistem',
        ];
    }

    /**
     * Relationship: Notifikasi milik satu user
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope: Filter notifikasi yang belum dibaca
     */
    public function scopeUnread($query)
    {
        return $query->whereNull('read_at');
    }

    /**
     * Scope: Filter notifikasi yang sudah dibaca
     */
    public function scopeRead($query)
    {
        return $query->whereNotNull('read_at');
    }

    /**
     * Scope: Filter berdasarkan tipe
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope: Urutkan berdasarkan yang terbaru
     */
    public function scopeLatest($query)
    {
        return $query->orderBy('created_at', 'desc');
    }

    /**
     * Tandai notifikasi sebagai sudah dibaca
     */
    public function markAsRead(): void
    {
        if ($this->read_at === null) {
            $this->update(['read_at' => now()]);
        }
    }

    /**
     * Tandai notifikasi sebagai belum dibaca
     */
    public function markAsUnread(): void
    {
        $this->update(['read_at' => null]);
    }

    /**
     * Cek apakah notifikasi sudah dibaca
     */
    public function isRead(): bool
    {
        return $this->read_at !== null;
    }

    /**
     * Cek apakah notifikasi belum dibaca
     */
    public function isUnread(): bool
    {
        return $this->read_at === null;
    }

    /**
     * Mendapatkan nama tipe yang diformat
     */
    public function getTypeNameAttribute(): string
    {
        $types = self::getTypes();
        return $types[$this->type] ?? $this->type;
    }

    /**
     * Static method untuk membuat notifikasi inquiry baru
     */
    public static function createInquiryNotification(int $userId, string $kostName, int $inquiryId): self
    {
        return self::create([
            'user_id' => $userId,
            'title' => 'Inquiry Baru',
            'message' => "Anda mendapat inquiry baru untuk kost {$kostName}",
            'type' => self::TYPE_INQUIRY,
            'data' => ['inquiry_id' => $inquiryId],
        ]);
    }

    /**
     * Static method untuk membuat notifikasi kost disetujui
     */
    public static function createKostApprovedNotification(int $userId, string $kostName): self
    {
        return self::create([
            'user_id' => $userId,
            'title' => 'Kost Disetujui',
            'message' => "Kost {$kostName} telah disetujui dan sekarang dapat dilihat oleh pencari kost",
            'type' => self::TYPE_KOST_APPROVED,
        ]);
    }

    /**
     * Static method untuk membuat notifikasi kost ditolak
     */
    public static function createKostRejectedNotification(int $userId, string $kostName, string $reason = ''): self
    {
        $message = "Kost {$kostName} ditolak";
        if ($reason) {
            $message .= ". Alasan: {$reason}";
        }

        return self::create([
            'user_id' => $userId,
            'title' => 'Kost Ditolak',
            'message' => $message,
            'type' => self::TYPE_KOST_REJECTED,
        ]);
    }
}
