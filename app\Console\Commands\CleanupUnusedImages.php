<?php

namespace App\Console\Commands;

use App\Services\FileUploadService;
use Illuminate\Console\Command;

class CleanupUnusedImages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'kost:cleanup-images {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cleanup unused images from storage';

    private FileUploadService $fileUploadService;

    public function __construct(FileUploadService $fileUploadService)
    {
        parent::__construct();
        $this->fileUploadService = $fileUploadService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧹 Starting image cleanup process...');

        if ($this->option('dry-run')) {
            $this->warn('🔍 DRY RUN MODE - No files will be deleted');
        }

        try {
            if ($this->option('dry-run')) {
                // For dry run, we'll just show what would be deleted
                $this->info('This would cleanup unused images from storage.');
                $this->info('Run without --dry-run to actually delete files.');
                return Command::SUCCESS;
            }

            $result = $this->fileUploadService->cleanupUnusedImages();

            if ($result['success']) {
                $this->info("✅ Successfully deleted {$result['deleted_count']} unused images.");

                if (!empty($result['errors'])) {
                    $this->warn('⚠️  Some errors occurred:');
                    foreach ($result['errors'] as $error) {
                        $this->error("   - {$error}");
                    }
                }
            } else {
                $this->error('❌ Failed to cleanup images: ' . ($result['error'] ?? 'Unknown error'));
                return Command::FAILURE;
            }

        } catch (\Exception $e) {
            $this->error('❌ An error occurred: ' . $e->getMessage());
            return Command::FAILURE;
        }

        $this->info('🎉 Image cleanup completed!');
        return Command::SUCCESS;
    }
}
