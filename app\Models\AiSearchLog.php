<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AiSearchLog extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'query',
        'ai_response',
        'results_count',
        'response_time',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected function casts(): array
    {
        return [
            'results_count' => 'integer',
            'response_time' => 'decimal:3',
        ];
    }

    /**
     * Relationship: Log pencarian milik satu user
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope: Filter berdasarkan user
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope: Urutkan berdasarkan yang terbaru
     */
    public function scopeLatest($query)
    {
        return $query->orderBy('created_at', 'desc');
    }

    /**
     * Scope: Filter berdasarkan range tanggal
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Static method untuk mencatat pencarian AI
     */
    public static function logSearch(?int $userId, string $query, ?string $aiResponse = null, int $resultsCount = 0, ?float $responseTime = null): self
    {
        return self::create([
            'user_id' => $userId,
            'query' => $query,
            'ai_response' => $aiResponse,
            'results_count' => $resultsCount,
            'response_time' => $responseTime,
        ]);
    }

    /**
     * Mendapatkan response time yang diformat
     */
    public function getFormattedResponseTimeAttribute(): string
    {
        if ($this->response_time === null) {
            return 'N/A';
        }

        return number_format($this->response_time, 3) . 's';
    }

    /**
     * Mendapatkan statistik pencarian untuk user tertentu
     */
    public static function getUserSearchStats(int $userId): array
    {
        $logs = self::where('user_id', $userId);

        return [
            'total_searches' => $logs->count(),
            'avg_response_time' => $logs->avg('response_time'),
            'total_results' => $logs->sum('results_count'),
            'last_search' => $logs->latest()->first()?->created_at,
        ];
    }
}
