<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class KostResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'owner_id' => $this->owner_id,
            'name' => $this->name,
            'description' => $this->description,
            'address' => $this->address,
            'city' => $this->city,
            'province' => $this->province,
            'postal_code' => $this->postal_code,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
            'price_monthly' => $this->price_monthly,
            'price_daily' => $this->price_daily,
            'room_count' => $this->room_count,
            'available_rooms' => $this->available_rooms,
            'gender_type' => $this->gender_type,
            'kost_type' => $this->kost_type,
            'status' => $this->status,
            'is_active' => $this->is_active,
            'formatted_price' => $this->formatted_price,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,

            // Relationships
            'owner' => $this->whenLoaded('owner', function () {
                return [
                    'id' => $this->owner->id,
                    'name' => $this->owner->name,
                    'email' => $this->owner->email,
                    'phone' => $this->owner->phone,
                ];
            }),

            'facilities' => $this->whenLoaded('facilities', function () {
                return $this->facilities->map(function ($facility) {
                    return [
                        'id' => $facility->id,
                        'name' => $facility->name,
                        'description' => $facility->description,
                        'icon' => $facility->icon,
                        'category' => $facility->category,
                        'category_name' => $facility->category_name,
                    ];
                });
            }),

            'images' => $this->whenLoaded('images', function () {
                return $this->images->map(function ($image) {
                    return [
                        'id' => $image->id,
                        'image_path' => $image->image_path,
                        'image_url' => $image->image_url,
                        'image_type' => $image->image_type,
                        'image_type_name' => $image->image_type_name,
                        'alt_text' => $image->alt_text,
                        'sort_order' => $image->sort_order,
                    ];
                });
            }),

            'inquiries_count' => $this->whenCounted('inquiries'),
            'facilities_count' => $this->whenCounted('facilities'),
            'images_count' => $this->whenCounted('images'),
        ];
    }
}
